import { Stack, useLocalSearchParams } from 'expo-router';
import React, { useMemo } from 'react';
import { View } from 'react-native';
import WithChatRoom from 'modules/game/pages/GroupPlayPage/WithChatRoom';
import WithGroupPlayChatContext from 'modules/game/hooks/useGroupPlayChatContext';
import { WithGameContext } from 'modules/game/hooks/useGameContext';

const GamePlayLayout = () => {
  const { id: gameId } = useLocalSearchParams();

  const WithChatRoomComponent = useMemo(
    () => WithGameContext(WithGroupPlayChatContext(WithChatRoom(Stack))),
    [],
  );

  return (
    <View style={{ flex: 1, justifyContent: 'space-between' }}>
      <WithChatRoomComponent
        screenOptions={{ headerShown: false }}
        gameId={gameId}
      />
    </View>
  );
};

export default React.memo(GamePlayLayout);
