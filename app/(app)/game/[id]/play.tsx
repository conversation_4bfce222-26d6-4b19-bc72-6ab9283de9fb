import React, { useEffect } from 'react';
import WithMatiksBackground from 'atoms/WithMatiksBackground';
import { INITIAL_ROUTE_KEY } from 'core/constants/appConstants';
import { Redirect, useLocalSearchParams } from 'expo-router';
import _isEmpty from 'lodash/isEmpty';
import { setStorageItemAsync } from 'core/hooks/useStorageState';
import GamePlayPage from 'modules/game/pages/GamePlayPage';

const WaitingForFriendContainer = () => {
  const searchParams = useLocalSearchParams();
  const { id: gameId } = searchParams;

  useEffect(() => {
    setStorageItemAsync(INITIAL_ROUTE_KEY, null);
  }, []);

  if (_isEmpty(gameId)) {
    return <Redirect href="/home" />;
  }

  return (
    <WithMatiksBackground>
      <GamePlayPage gameId={gameId} />
    </WithMatiksBackground>
  );
};

export default React.memo(WaitingForFriendContainer);
