import React, { useEffect } from 'react';

import { Redirect, useLocalSearchParams } from 'expo-router';
import _isEmpty from 'lodash/isEmpty';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { PAGE_NAMES } from 'core/constants/pageNames';
import ShowdownDetails from 'modules/showdownV2/pages/showdown';

const ShowdownDetailsPage = (props) => {
  const searchParams = useLocalSearchParams();
  const { id: showdownId } = searchParams;

  useEffect(() => {
    webengage?.screen?.(PAGE_NAMES.SHOWDOWN_DETAILS);
    Analytics.track(ANALYTICS_EVENTS.SHOWDOWN.VISITED_SHOWDOWN_DETAIL_PAGE, {
      showdownId,
    });
  }, [showdownId]);

  if (_isEmpty(showdownId)) {
    return <Redirect href="/contests" />;
  }
  return <ShowdownDetails showdownId={showdownId} />;
};

export default React.memo(ShowdownDetailsPage);
