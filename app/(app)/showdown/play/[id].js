/* eslint-disable import/no-unused-modules */
import React, { useEffect } from 'react';
import JoinGameV2 from 'modules/game/pages/JoinGameV2';
import WithMatiksBackground from 'atoms/WithMatiksBackground';
import { INITIAL_ROUTE_KEY } from 'core/constants/appConstants';
import { Redirect, useLocalSearchParams } from 'expo-router';
import _isEmpty from 'lodash/isEmpty';
import { setStorageItemAsync } from 'core/hooks/useStorageState';
import { WithGameContextV2 } from 'modules/game/hooks/useGameContextV2';

const WithGameContextGame = WithGameContextV2(JoinGameV2);

const WaitingForFriendContainer = () => {
  const searchParams = useLocalSearchParams();
  const { id: gameId } = searchParams;

  useEffect(() => {
    setStorageItemAsync(INITIAL_ROUTE_KEY, null);
  }, []);

  if (_isEmpty(gameId)) {
    return <Redirect href="/home" />;
  }

  return (
    <WithMatiksBackground>
      {/* <Header title={'Game'} goBack={goBack}/> */}
      <WithGameContextGame gameId={gameId} />
    </WithMatiksBackground>
  );
};

export default React.memo(WaitingForFriendContainer);
