import React, { useEffect } from 'react';
import SearchOpponent from '@/src/modules/search/pages/SearchOpponent';
import WithMatiksBackground from '@/src/components/atoms/WithMatiksBackground';

import { router, useLocalSearchParams } from 'expo-router';
import _isEmpty from 'lodash/isEmpty';
import _toNumber from 'lodash/toNumber';
import isValidTimeLimit from 'core/utils/isValidTimeLimit';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import { GAME_TYPES } from 'core/constants/gameTypes';
import { PAGE_NAMES } from '../../../src/core/constants/pageNames';

const SearchingForPlayerContainer = (props) => {
  const searchParams = useLocalSearchParams();
  const { timeLimit, gameType } = searchParams;

  useEffect(() => {
    webengage?.screen?.(PAGE_NAMES.SEARCH_OPPONENT_PAGE);
  }, []);

  if (_isEmpty(timeLimit)) {
    return router.replace('/home');
  }

  const isValidTime = isValidTimeLimit({ timeLimit, gameType });

  if (!isValidTime) {
    showToast({ description: 'Invalid time limit', type: TOAST_TYPE.ERROR });
    return router.replace('/home');
  }

  return (
    <WithMatiksBackground>
      <SearchOpponent
        timeLimit={_toNumber(timeLimit)}
        gameType={gameType ?? GAME_TYPES.DMAS}
      />
    </WithMatiksBackground>
  );
};

export default React.memo(SearchingForPlayerContainer);
