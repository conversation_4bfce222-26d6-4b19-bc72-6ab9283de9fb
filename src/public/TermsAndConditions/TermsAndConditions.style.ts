import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  h1: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 8,
    marginTop: 16,
    lineHeight: 34,
    flexWrap: 'wrap',
  },
  h2: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 8,
    marginTop: 24,
    lineHeight: 30,
    flexWrap: 'wrap',
  },
  h3: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 6,
    marginTop: 20,
    lineHeight: 26,
    flexWrap: 'wrap',
  },
  h4: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 6,
    marginTop: 16,
    lineHeight: 24,
    flexWrap: 'wrap',
  },
  paragraph: {
    fontSize: 16,
    color: '#333333',
    lineHeight: 24,
    marginBottom: 12,
    flexWrap: 'wrap',
  },
  lastUpdated: {
    fontSize: 14,
    color: '#666666',
    fontStyle: 'italic',
    marginBottom: 16,
  },
  listContainer: {
    marginBottom: 12,
  },
  listItem: {
    flexDirection: 'row',
    marginBottom: 8,
    paddingLeft: 16,
    alignItems: 'flex-start',
  },
  bullet: {
    fontSize: 16,
    color: '#333333',
    marginRight: 8,
    marginTop: 2,
    minWidth: 10,
  },
  listItemText: {
    fontSize: 16,
    color: '#333333',
    lineHeight: 24,
    flex: 1,
    flexWrap: 'wrap',
  },
  strong: {
    fontWeight: 'bold',
  },
  link: {
    color: '#007AFF',
    textDecorationLine: 'underline',
  },
});

export default styles;
