// storage.ts
import useZustandUserStore from "./useUserStore/useZustandUserStore";
import useZustandFeedStore from "./useFeedStore/useZustandFeedStore";
import useZustandShowdownStore from "./useShowdownStore/useZustandShowdownStore";
import useGameZustandStore from "./useGameStore/useZustandGameStore";

export const clearCentralStore = () => {
  useZustandUserStore.getState().clearStore();
  useZustandFeedStore.getState().clearStore();
  useZustandShowdownStore.getState().clearStore();
  useGameZustandStore.getState().clearStore();
}
