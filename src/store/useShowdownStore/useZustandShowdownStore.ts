/* eslint-disable no-param-reassign */
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { ShowdownState } from './types';
import ShowdownHandlers from './handlers';
import { clearStore } from '../helpers';

const defaultOtherConfig = {
  isLive: false,
  hasEnded: false,
  currentRound: 0,
  canUserJoin: false,
  roundEndsAt: 0,
  breakEndsAt: 0,
  isRoundActive: false,
  hasRegistrationEnded: false,
  isBreak: false,
  hasUserRegistered: false,
  joinUntilTimestamp: 0,
};

const defaultConfig = {
  showdown: null,
  fixtures: null,
  leaderboard: [],
  showdownLoading: false,
  fixturesLoading: false,
  leaderboardLoading: false,
  showdownError: null,
  fixturesError: null,
  leaderboardError: null,
  otherStates: defaultOtherConfig,
  isRegisteringForShowDown: false,
  isUnregisteringFromShowDown: false,
  isFixtureAvailable: false,
  isLeaderboardAvailable: false,
  totalPages: 1,
  leaderboardPageNumber: 1,
  showdownId: '',
  isShowdownAvailable: false,
};

const useShowdownZustandStore = create<ShowdownState>()(
  immer((set, get) => {
    const handlers = new ShowdownHandlers(set, get);
    return {
      ...defaultConfig,
      ...handlers,
      clearStore: () => {
        clearStore(set, defaultConfig);
      },
    };
  }),
);

export default useShowdownZustandStore;
