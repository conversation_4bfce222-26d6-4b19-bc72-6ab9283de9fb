/* eslint-disable no-param-reassign */
import { handleAsync } from 'core/utils/asyncUtils';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import showdownReader from 'modules/showdownV2/readers/showdownReader';
import { SetShowdownStore, GetShowdownStore, ShowdownState } from './types';
import { ShowdownApi, ShowdownApiInterface } from './api';

interface HandlersInterface {
  fetchShowdown: (_showdownId: string) => Promise<void>;
  fetchFixtures: (_showdownId: string) => Promise<void>;
  fetchLeaderboard: (_showdownId: string, _page: number) => Promise<void>;
  updateIsLive: (_isLive: boolean) => Promise<void>;
  updateHasEnded: (_hasEnded: boolean) => Promise<void>;
  registerForShowdown: (_showdownId: string) => Promise<boolean>;
  unregisterFromShowdown: (_showdownId: string) => Promise<boolean>;
  updateOtherStates: (_func: (_otherStates: any) => void) => Promise<void>;
  getShowdownPropertiesToTrack: () => any;
  updateShowdown: (_updateFunc: (_prev: any) => any) => Promise<void>;
  setShowdownId: (_showdownId: string) => Promise<void>;
  refetchData: (_showdownId: string) => Promise<void>;
}

export default class ShowdownHandlers implements HandlersInterface {
  private api: ShowdownApiInterface;

  private setState: SetShowdownStore;

  private getState: GetShowdownStore;

  constructor(setState: SetShowdownStore, getState: GetShowdownStore) {
    this.api = new ShowdownApi();
    this.setState = setState;
    this.getState = getState;
  }

  setShowdownId = async (_showdownId: string) => {
    this.setState((state: ShowdownState) => {
      state.showdownId = _showdownId;
    });
  };

  refetchData = async (_showdownId: string) => {
    if (!_showdownId) {
      return;
    }
    this.getState().clearStore();
    await this.fetchShowdown(_showdownId);
    await this.fetchFixtures(_showdownId);
    await this.fetchLeaderboard(_showdownId, 1);
  };

  fetchShowdown = async (_showdownId: string) => {
    const loading = this.getState().showdownLoading;
    if (loading) {
      return;
    }
    this.setState((state: ShowdownState) => {
      state.showdownLoading = true;
      state.showdownId = _showdownId;
    });
    const [resp, error] = await handleAsync(
      this.api.fetchShowdown,
      _showdownId,
    );
    if (error) {
      this.setState((state) => {
        state.showdownLoading = false;
        state.showdownError = error;
      });
      return;
    }

    const showdown = _get(resp, ['data', 'getShowdownById'], null);
    const hasUserRegistered = !_isEmpty(
      showdownReader.currentUserParticipation(showdown),
    );
    const isShowdownAvailable = !_isEmpty(showdown);
    this.setState((state) => {
      state.showdown = showdown;
      state.showdownLoading = false;
      state.showdownError = null;
      state.otherStates.hasUserRegistered = hasUserRegistered;
      state.isShowdownAvailable = isShowdownAvailable;
    });
  };

  updateShowdown = async (_updateFunc: (_prev: any) => any) => {
    this.setState((state: ShowdownState) => {
      state.showdown = _updateFunc(state.showdown);
    });
  };

  fetchFixtures = async (_showdownId: string) => {
    const state = this.getState();
    const loading = state.fixturesLoading;
    let showdownId = state?.showdownId;

    if (loading || !_showdownId) {
      return;
    }
    if (_isEmpty(showdownId)) {
      showdownId = _showdownId;
    }

    const showdownStartTime = state.showdown?.startTime
      ? new Date(state.showdown.startTime).getTime()
      : null;
    const currentTime = getCurrentTime();
    if (!showdownStartTime || showdownStartTime - currentTime > 2 * 60 * 1000) {
      return;
    }
    this.setState((_state: ShowdownState) => {
      _state.fixturesLoading = true;
    });
    const [resp, error] = await handleAsync(this.api.fetchFixtures, showdownId);
    if (error) {
      this.setState((_state: ShowdownState) => {
        _state.fixturesLoading = false;
      });
      return;
    }

    const currentFixture = _get(
      resp,
      ['data', 'getFicturesByShowdownId', 'currentUserFicture'],
      null,
    );
    const isFixtureAvailable = !_isEmpty(currentFixture);

    this.setState((_state: ShowdownState) => {
      _state.fixtures = currentFixture;
      _state.fixturesLoading = false;
      _state.isFixtureAvailable = isFixtureAvailable;
    });
  };

  fetchLeaderboard = async (showdownId: string, page: number) => {
    const loading = this.getState().leaderboardLoading;
    if (loading) {
      return;
    }
    this.setState((_state: ShowdownState) => {
      _state.leaderboardLoading = true;
      _state.leaderboardPageNumber = page;
    });
    const [resp, error] = await handleAsync(
      this.api.fetchLeaderboard,
      showdownId,
      page,
    );
    if (error) {
      this.setState((state) => {
        state.leaderboardLoading = false;
        state.leaderboardError = error;
        state.leaderboard = [];
      });
      return;
    }
    const totalPages = Math.ceil(
      _get(
        resp,
        ['data', 'getPaginatedLeaderboard', 'count'],
        this.getState().showdown?.registrationCount ?? 0,
      ) / 100,
    );
    const leaderboard = _get(
      resp,
      ['data', 'getPaginatedLeaderboard', 'participants'],
      [],
    );
    this.setState((state) => {
      state.leaderboard = leaderboard;
      state.leaderboardLoading = false;
      state.totalPages = totalPages;
    });
  };

  registerForShowdown = async (showdownId: string) => {
    const loading = this.getState().isRegisteringForShowDown;
    if (loading) {
      return false;
    }
    this.setState((state) => {
      state.isRegisteringForShowDown = true;
    });
    const [resp, error] = await handleAsync(
      this.api.registerForShowdown,
      showdownId,
    );
    if (error) {
      this.setState((state) => {
        state.isRegisteringForShowDown = false;
      });
      throw error;
    }
    const isUserRegistered = _get(resp, ['data', 'registerForShowdown'], false);

    this.setState((state) => {
      state.isRegisteringForShowDown = false;
      state.otherStates.hasUserRegistered = isUserRegistered;
    });
    return isUserRegistered;
  };

  unregisterFromShowdown = async (showdownId: string) => {
    const loading = this.getState().isUnregisteringFromShowDown;
    if (loading) {
      return false;
    }
    this.setState((state) => {
      state.isUnregisteringFromShowDown = true;
    });
    const [resp, error] = await handleAsync(
      this.api.unregisterFromShowdown,
      showdownId,
    );
    if (error) {
      this.setState((state) => {
        state.isUnregisteringFromShowDown = false;
      });
      throw error;
    }
    const isUserUnregistered = _get(
      resp,
      ['data', 'unregisterFromShowdown'],
      false,
    );

    this.setState((state) => {
      state.isUnregisteringFromShowDown = false;
      state.otherStates.hasUserRegistered = isUserUnregistered;
    });
    return isUserUnregistered;
  };

  updateIsLive = async (isLive: boolean) => {
    this.setState((state) => {
      state.otherStates.isLive = isLive;
    });
  };

  updateHasEnded = async (hasEnded: boolean) => {
    this.setState((state) => {
      state.otherStates.hasEnded = hasEnded;
    });
  };

  updateOtherStates = async (_func: (_otherStates: any) => void) => {
    this.setState((state) => {
      _func(state.otherStates);
    });
  };

  getShowdownPropertiesToTrack = () => {
    const { showdown } = this.getState();
    if (_isEmpty(showdown)) {
      return EMPTY_OBJECT;
    }

    return {
      showdownStatus: showdown?.status,
      showdownName: showdown?.name,
      showdownId: showdown?._id,
    };
  };
}
