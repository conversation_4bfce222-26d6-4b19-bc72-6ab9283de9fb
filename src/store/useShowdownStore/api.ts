/* eslint-disable class-methods-use-this */
import { ApolloC<PERSON>, ApolloQueryResult, FetchResult } from '@apollo/client';
import {
  GET_SHOWDOWN,
  GET_FIXTURES,
  GET_LEADERBOARD_DETAILS,
  GET_FEATURED_SHOWDOWN,
  REGISTER_FOR_SHOWDOWN,
  UNREGISTER_FROM_SHOWDOWN,
} from './graphql';

export interface ShowdownApiInterface {
  // Queries
  fetchShowdown: (_showdownId: string) => Promise<ApolloQueryResult<any>>;
  fetchFeaturedShowdown: () => Promise<ApolloQueryResult<any>>;
  fetchFixtures: (_showdownId: string) => Promise<ApolloQueryResult<any>>;
  fetchLeaderboard: (
    _showdownId: string,
    _page: number,
  ) => Promise<ApolloQueryResult<any>>;

  // Mutations
  registerForShowdown: (
    _showdownId: string,
    _formData: any,
  ) => Promise<FetchResult<any>>;
  unregisterFromShowdown: (_showdownId: string) => Promise<FetchResult<any>>;
}

export class ShowdownApi implements ShowdownApiInterface {
  fetchShowdown = async (_showdownId: string) => {
    const apolloClient: ApolloClient<object> = getApolloClient();
    const resp = await apolloClient.query({
      query: GET_SHOWDOWN,
      variables: { showdownId: _showdownId },
      fetchPolicy: 'network-only',
    });
    return resp;
  };

  fetchFeaturedShowdown: () => Promise<ApolloQueryResult<any>> = async () => {
    const apolloClient: ApolloClient<object> = getApolloClient();
    const resp = await apolloClient.query({
      query: GET_FEATURED_SHOWDOWN,
      fetchPolicy: 'network-only',
    });
    return resp;
  };

  fetchFixtures: (_showdownId: string) => Promise<ApolloQueryResult<any>> =
    async (_showdownId: string) => {
      const apolloClient: ApolloClient<object> = getApolloClient();
      const resp = await apolloClient.query({
        query: GET_FIXTURES,
        variables: { showdownId: _showdownId },
        fetchPolicy: 'network-only',
      });
      return resp;
    };

  fetchLeaderboard: (
    _showdownId: string,
    _page: number,
  ) => Promise<ApolloQueryResult<any>> = async (showdownId, page) => {
    const apolloClient: ApolloClient<object> = getApolloClient();
    const resp = await apolloClient.query({
      query: GET_LEADERBOARD_DETAILS,
      variables: {
        input: {
          showdownId,
          page,
        },
      },
      fetchPolicy: 'network-only',
    });
    return resp;
  };

  registerForShowdown: (_showdownId: string) => Promise<FetchResult<any>> =
    async (showdownId: string) => {
      const apolloClient: ApolloClient<object> = getApolloClient();
      const resp = await apolloClient.mutate({
        mutation: REGISTER_FOR_SHOWDOWN,
        variables: {
          input: {
            showdownId,
          },
        },
        fetchPolicy: 'network-only',
      });
      return resp;
    };

  unregisterFromShowdown: (_showdownId: string) => Promise<FetchResult<any>> =
    async (_showdownId: string) => {
      const apolloClient: ApolloClient<object> = getApolloClient();
      const resp = await apolloClient.mutate({
        mutation: UNREGISTER_FROM_SHOWDOWN,
        variables: { showdownId: _showdownId },
        fetchPolicy: 'network-only',
      });
      return resp;
    };
}
