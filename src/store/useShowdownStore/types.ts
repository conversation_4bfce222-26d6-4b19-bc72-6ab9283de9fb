type OtherStates = {
  isLive: boolean;
  hasEnded: boolean;
  currentRound: number;
  canUserJoin: boolean;
  roundEndsAt: number;
  breakEndsAt: number;
  isRoundActive: boolean;
  hasRegistrationEnded: boolean;
  isBreak: boolean;
  hasUserRegistered: boolean;
  joinUntilTimestamp: number;
};

export type ShowdownState = {
  showdownId: string;
  showdown: any | null;
  fixtures: any | null;
  leaderboard: any[];
  showdownLoading: boolean;
  fixturesLoading: boolean;
  leaderboardLoading: boolean;
  showdownError: Error | null;
  fixturesError: Error | null;
  leaderboardError: Error | null;
  isFixtureAvailable: boolean;
  isLeaderboardAvailable: boolean;
  isShowdownAvailable: boolean;
  leaderboardPageNumber: number;
  totalPages: number;
  getShowdownPropertiesToTrack: () => any;
  isRegisteringForShowDown: boolean;
  registerForShowdown: (_showdownId: string) => Promise<boolean>;
  isUnregisteringFromShowDown: boolean;
  unregisterFromShowdown: (_showdownId: string) => Promise<boolean>;
  fetchShowdown: (_showdownId: string) => Promise<void>;
  fetchFixtures: (_showdownId: string) => Promise<void>;
  fetchLeaderboard: (_showdownId: string, _page: number) => Promise<void>;
  clearStore: () => void;
  otherStates: OtherStates;
  updateIsLive: (_isLive: boolean) => Promise<void>;
  updateHasEnded: (_hasEnded: boolean) => Promise<void>;
  updateOtherStates: (_func: (_otherStates: any) => void) => Promise<void>;
  updateShowdown: (_updateFunc: (_prev: any) => any) => Promise<void>;
  setShowdownId: (_showdownId: string) => Promise<void>;
  refetchData: (_showdownId: string) => Promise<void>;
};

export type ShowdownStore = (_state: ShowdownState) => any;

export type GetShowdownStore = (_state?: ShowdownState) => ShowdownState;
export type SetShowdownStore = (
  _update: ShowdownState | ((_state: ShowdownState) => void),
) => void;
