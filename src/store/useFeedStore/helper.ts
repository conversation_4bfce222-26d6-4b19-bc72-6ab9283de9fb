/* eslint-disable class-methods-use-this */
import _get from 'lodash/get';
import _toNumber from 'lodash/toNumber';

export interface FeedHelperInterface {
  changeFeedLikeStatus: (_feeds: any[], _feedId: string) => any[];
}

export class FeedHelper implements FeedHelperInterface {
  changeFeedLikeStatus = (_feeds: any[], _feedId: string): any[] =>
    _feeds.map((feed: any) => {
      if (feed._id === _feedId) {
        return {
          ...feed,
          isLiked: !feed.isLiked,
          feedData: {
            ...feed?.feedData,
            likesCount: Math.max(
              0,
              feed.isLiked
                ? Math.max(
                    _toNumber(_get(feed, ['feedData', 'likesCount'], 0)) - 1,
                    0,
                  )
                : _toNumber(_get(feed, ['feedData', 'likesCount'], 0)) + 1,
            ),
          },
        };
      }
      return feed;
    });
}
