/* eslint-disable class-methods-use-this */
import { ApolloClient } from '@apollo/client';
import {
  GET_USER_FEED,
  UPDATE_LAST_READ_FEED_ID,
  UPDATE_LIKE_STATUS,
} from './graphql';

export interface FeedApiInterface {
  fetchFeeds: (_lastId: string | null, _pageSize: number) => Promise<any>;
  updateLikeStatus: (_feedId: string) => Promise<boolean>;
  updateLastReadFeedId: (_feedId: string) => Promise<boolean>;
}

export default class FeedApi implements FeedApiInterface {
  fetchFeeds = async (lastId: string | null, pageSize: number) => {
    const apolloClient: ApolloClient<object> = getApolloClient();
    const { data } = await apolloClient.query({
      variables: { lastId, pageSize },
      query: GET_USER_FEED,
      fetchPolicy: 'network-only',
    });
    return data.getUserFeeds;
  };

  updateLikeStatus = async (feedId: string) => {
    const apolloClient: ApolloClient<object> = getApolloClient();
    const { data } = await apolloClient.mutate({
      mutation: UPDATE_LIKE_STATUS,
      variables: { feedId },
      fetchPolicy: 'network-only',
    });
    return data?.updateLikeStatus;
  };

  updateLastReadFeedId = async (feedId: string) => {
    const apolloClient: ApolloClient<object> = getApolloClient();
    const { data } = await apolloClient.mutate({
      mutation: UPDATE_LAST_READ_FEED_ID,
      variables: { lastReadFeedId: feedId },
      fetchPolicy: 'network-only',
    });
    return data?.updateLastReadFeedId;
  };
}
