export type FeedState = {
  feeds: any[];
  lastId: string | null;
  hasMore: boolean;
  loading: boolean;
  isInAppToastShown: boolean;
  inAppMessageQueue: any[];
  error: Error | null;
  isRead: boolean;
  fetchFeeds: () => Promise<void>;
  updateFeedLikeStatus: (_feedId: string) => Promise<void>;
  updateLastReadFeedId: (_feedId: string) => Promise<void>;
  onInAppMessage: (_message: any) => void;
  emptyInAppMessageQueue: () => void;
  showInAppToast: () => void;
  clearStore: () => void;
};

export type FeedStore = (_state: FeedState) => any;

export type GetFeedStore = (_state?: FeedState) => FeedState;
export type SetFeedStore = (
  _update: FeedState | ((_state: FeedState) => void),
) => void;
