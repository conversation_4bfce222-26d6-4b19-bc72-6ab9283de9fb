/* eslint-disable no-param-reassign */
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import FeedHandlers from './handlers';
import { FeedState } from './types';
import { clearStore } from '../helpers';

const defaultConfig = {
  feeds: [],
  lastId: null,
  hasMore: true,
  loading: false,
  error: null,
  isRead: true,
  isInAppToastShown: false,
  inAppMessageQueue: [],
};

// use immer
const useFeedZustandStore = create<FeedState>()(
  immer((set, get) => {
    const handlers = new FeedHandlers(set, get);

    return {
      clearStore: () => clearStore(set, defaultConfig),
      ...defaultConfig,
      ...handlers,
    };
  }),
);

export default useFeedZustandStore;
