/* eslint-disable class-methods-use-this */
import { ApolloClient, ApolloQueryResult } from '@apollo/client';
import { FETCH_GAME_QUERY } from './graphql';

export interface GameApiInterface {
  fetchGame: (_gameId: string) => Promise<ApolloQueryResult<any>>;
}

export default class <PERSON><PERSON><PERSON> implements GameApiInterface {
  fetchGame = async (_gameId: string) => {
    const apolloClient: ApolloClient<object> = getApolloClient();
    const resp = await apolloClient.query({
      query: FETCH_GAME_QUERY,
      variables: { gameId: _gameId },
      fetchPolicy: 'network-only',
    });
    return resp;
  };
}
