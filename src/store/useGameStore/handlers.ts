/* eslint-disable no-param-reassign */
import {
  hideToast,
  showToast,
  TOAST_TYPE,
} from '@/src/components/molecules/Toast';
import { handleAsync } from 'core/utils/asyncUtils';
import _get from 'lodash/get';
// import { decryptJsonData } from 'core/utils/encryptions/decrypt';
import gameReader from '@/src/core/readers/gameReader';
import { decryptJsonData } from '@/src/core/utils/encryptions/decrypt';
import _map from 'lodash/map';
import _reduce from 'lodash/reduce';
import _isNil from 'lodash/isNil';
import { GetGameState, SetGameState } from './types';
import GameA<PERSON>, { GameApiInterface } from './api';

interface HandlersInterface {
  fetchGame: (_gameId: string) => Promise<void>;
  updateCurrentGame: (_updateFunc: (_prev: any) => any) => Promise<void>;
}

export default class GameHandlers implements HandlersInterface {
  private api: GameApiInterface;

  private setState: SetGameState;

  private getState: GetGameState;

  constructor(setState: SetGameState, getState: GetGameState) {
    this.api = new GameApi();
    this.setState = setState;
    this.getState = getState;
  }

  fetchGame = async (gameId: string) => {
    const currentGameState = this.getState();
    if (currentGameState.loading) return;
    this.setState((state) => {
      state.loading = true;
    });
    const [resp, error]: [any, Error | null] = await handleAsync(
      this.api.fetchGame,
      gameId,
    );
    if (error) {
      hideToast();
      showToast({
        type: TOAST_TYPE.ERROR,
        description: error.message,
      });
      this.setState((state) => {
        state.loading = false;
        state.error = error;
      });
      return;
    }

    const game = _get(resp, ['data', 'game'], null);
    const { encryptedQuestions, ...coreGameDataFields } = game ?? EMPTY_OBJECT;
    const questions = _map(encryptedQuestions, decryptJsonData);

    this.setState((state) => {
      state.currentGame = { ...coreGameDataFields, questions };
      state.initialGame = { ...coreGameDataFields, questions };
      state.lastUpdatedAt = Date.now();
      state.loading = false;
      state.error = null;
    });
  };

  updateCurrentGame = async (newGame: any) => {
    this.setState((state) => {
      const prevGameId = gameReader.id(state.currentGame);
      const gameId = gameReader.id(newGame);
      if (prevGameId !== gameId) {
        state.lastUpdatedAt = Date.now();
        state.currentGame = newGame;
        return;
      }
      const newGameWithNonNillValues = _reduce(
        newGame,
        (acc, val, key) => {
          if (!_isNil(val)) {
            acc[key] = val;
          }
          return acc;
        },
        {},
      );
      state.currentGame = {
        ...(state.currentGame ?? EMPTY_OBJECT),
        ...newGameWithNonNillValues,
      };
    });
  };

  //   decryptQuestion = async (_question: any, type: string) => {
  //     this.setState((state) => {
  //         if (type)
  //       const decryptedQuestion = decryptJsonData(_question);
  //       state.game = decryptedQuestion;
  //     });
  //   };
}
