export type GameState = {
  initialGame: any;
  currentGame: any;
  loading: boolean;
  error: Error | null;
  lastUpdatedAt: number;
  clearStore: () => void;
  fetchGame: (_gameId: string) => Promise<void>;
  updateCurrentGame: (_updateFunc: (_prev: any) => any) => Promise<void>;
};

export type GameStore = (_state: GameState) => any;

export type GetGameState = (_state?: GameState) => GameState;
export type SetGameState = (
  _update: GameState | ((_state: GameState) => void),
) => void;
