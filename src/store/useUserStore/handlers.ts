import _get from 'lodash/get';
import { handleAsync } from '../../core/utils/asyncUtils';
import UserApi, { UserApiInterface } from './api';
import { UserState, SetUserStore, ShowInAppToastValues } from './types';

interface UserHandlersInterface {
  fetchUserRecentPresets: () => Promise<void>;
  fetchUserSavedPresets: () => Promise<void>;
  toggleMode: (_mode: boolean) => void;
  showInAppToast: (_values: ShowInAppToastValues) => void;
  updateWasmReady: (_status: boolean) => void;
}

export default class UserHandlers implements UserHandlersInterface {
  private api: UserApiInterface;

  private setState: SetUserStore;

  constructor(setState: SetUserStore) {
    this.api = new UserApi();
    this.setState = setState;
  }

  toggleMode = (mode: boolean) => {
    this.setState((state: UserState) => ({ ...state, isOnline: mode }));
  };

  updateNetworkStatusOfInternet = (status: boolean) => {
    this.setState((state: UserState) => ({
      ...state,
      isNetworkReachable: status,
    }));
  };

  updateNetworkStatusOfMatiks = (status: boolean) => {
    this.setState((state: UserState) => ({
      ...state,
      isMatiksReachable: status,
    }));
  };

  fetchUserRecentPresets = async () => {
    this.setState((state: UserState) => ({
      ...state,
      recentPresetsLoading: true,
      recentPresetsError: null,
    }));
    const [resp, error]: [any, Error | null] = await handleAsync(
      this.api.fetchUserRecentPresets,
    );

    if (error || resp?.error) {
      this.setState((state: UserState) => ({
        ...state,
        recentPresetsError: error ?? resp?.error,
        recentPresetsLoading: false,
      }));
      return;
    }

    const recentPresets = _get(
      resp,
      ['data', 'userPresets', 'userPresets'],
      [],
    );
    this.setState((state: UserState) => ({
      ...state,
      recentPresets,
      recentPresetsLoading: false,
    }));
  };

  fetchUserSavedPresets = async () => {
    this.setState((state: UserState) => ({
      ...state,
      savedPresetsLoading: true,
      savedPresetsError: null,
    }));
    const [resp, error]: [any, Error | null] = await handleAsync(
      this.api.fetchUserSavedPresets,
    );

    if (error || resp?.error) {
      this.setState((state: UserState) => ({
        ...state,
        savedPresetsError: error ?? resp?.error,
        savedPresetsLoading: false,
      }));
      return;
    }

    const savedPresets = _get(resp, ['data', 'userPresets', 'userPresets'], []);
    this.setState((state: UserState) => ({
      ...state,
      savedPresets,
      savedPresetsLoading: false,
    }));
  };

  showInAppToast: (_values: ShowInAppToastValues) => void = (values) => {
    this.setState((state: UserState) => ({
      ...state,
      showToastValues: values,
    }));
  };

  updateWasmReady: (_status: boolean) => void = (status) => {
    this.setState((state: UserState) => ({ ...state, isWasmReady: status }));
  };
}
