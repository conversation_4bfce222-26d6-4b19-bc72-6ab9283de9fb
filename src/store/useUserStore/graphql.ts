import { USER_PRESET_FRAGMENT } from 'core/graphql/fragments/presets';
// import { USER_FRAGMENT } from 'core/graphql/fragments/user';
import { gql } from '@apollo/client';

// export const FETCH_USER_BY_ID_QUERY = gql`
//   ${USER_FRAGMENT}
//   query GetUserById($userId: ID!) {
//     user: getUserById(userId: $userId) {
//       ...CoreUserFields
//     }
//   }
// `;

export const GET_USER_RECENT_PRESETS_QUERY = gql`
  ${USER_PRESET_FRAGMENT}
  query GetUserRecentPresets {
    userPresets: getUserRecentPresets {
      userPresets {
        ...UserPresetFields
      }
      totalCount
    }
  }
`;

export const GET_USER_SAVED_PRESETS_QUERY = gql`
  ${USER_PRESET_FRAGMENT}
  query GetUserSavedPresets($page: Int, $pageSize: Int) {
    userPresets: getUserSavedPresets(page: $page, pageSize: $pageSize) {
      userPresets {
        ...UserPresetFields
      }
      totalCount
    }
  }
`;
