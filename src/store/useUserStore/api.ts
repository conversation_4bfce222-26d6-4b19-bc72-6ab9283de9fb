/* eslint-disable class-methods-use-this */
import { ApolloClient, ApolloQueryResult } from '@apollo/client';
import {
  GET_USER_RECENT_PRESETS_QUERY,
  GET_USER_SAVED_PRESETS_QUERY,
} from './graphql';

export interface UserApiInterface {
  fetchUserRecentPresets: () => Promise<ApolloQueryResult<any>>;
  fetchUserSavedPresets: () => Promise<ApolloQueryResult<any>>;
}

export default class UserApi implements UserApiInterface {
  async fetchUserRecentPresets() {
    const apolloClient: ApolloClient<object> = getApolloClient();
    const resp = await apolloClient.query({
      query: GET_USER_RECENT_PRESETS_QUERY,
      fetchPolicy: 'network-only',
    });
    return resp;
  }

  async fetchUserSavedPresets() {
    const apolloClient: ApolloClient<object> = getApolloClient();
    const resp = await apolloClient.query({
      query: GET_USER_SAVED_PRESETS_QUERY,
      fetchPolicy: 'network-only',
    });
    return resp;
  }
}
