/* eslint-disable import/prefer-default-export */
/* eslint-disable no-unused-vars */
import { WEBSOCKET_EVENTS } from '@/src/core/constants/events';
import { CHANNEL_TYPES } from './constants';
import { CHANNEL_TYPE } from './types';

export const getChannelType = (channel: any): CHANNEL_TYPE => {
  if (channel.startsWith(WEBSOCKET_EVENTS.USER_EVENT)) {
    return CHANNEL_TYPES.USER_CHANNEL;
  }
  if (channel.startsWith(WEBSOCKET_EVENTS.GAME_EVENT)) {
    return CHANNEL_TYPES.GAME_CHANNEL;
  }
  if (channel.startsWith(WEBSOCKET_EVENTS.SHOWDOWN_EVENT)) {
    return CHANNEL_TYPES.SHOWDOWN_CHANNEL;
  }
  if (channel.startsWith(WEBSOCKET_EVENTS.GROUP_CHAT_EVENT)) {
    return CHANNEL_TYPES.GROUP_CHAT_CHANNEL;
  }
  if (channel.startsWith(WEBSOCKET_EVENTS.ONLINE_USERS)) {
    return CHANNEL_TYPES.ONLINE_USERS_CHANNEL;
  }
  return CHANNEL_TYPES.NON_CHANNEL;
};
