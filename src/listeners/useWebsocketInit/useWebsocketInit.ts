import Analytics from '@/src/core/analytics';
import { ANALYTICS_EVENTS } from '@/src/core/analytics/const';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import useWebsocketStore from 'store/useWebSocketStore';
import _toNumber from 'lodash/toNumber';
import EventManager from '@/src/core/event';
import { usePathname } from 'expo-router';
import _isEmpty from 'lodash/isEmpty';
import _toString from 'lodash/toString';
import { getUserCurrentActivity } from '@/src/core/utils/getUserCurrentActivity';
import useRefetchOnAppFocus from '@/src/core/hooks/useRefetchOnAppFocus';
import { getChannelType } from './utils';
import handleEventEmitter from './eventEmmiters';

let serverOffset = 0;
global.getCurrentTime = () => Date.now() + _toNumber(serverOffset);

const pingChannel = `ping-${Date.now()}`;

const useWebsocketInit = () => {
  const {
    connect,
    joinChannel,
    isConnected,
    sendMessage,
    cleanUp,
    updateLastMessage,
    leaveChannel,
  } = useWebsocketStore((state) => ({
    connect: state.connect,
    joinChannel: state.joinChannel,
    isConnected: state.isConnected,
    sendMessage: state.sendMessage,
    cleanUp: state.cleanUp,
    updateLastMessage: state.updateLastMessage,
    leaveChannel: state.leaveChannel,
  }));

  useRefetchOnAppFocus(connect);

  const currentUrl = usePathname();
  const pingPongTimeoutRef = useRef<any>(null);

  const emitter = useMemo(() => new EventManager(), []);

  const { session, userId } = useSession();
  const handleServerTimeOffset = useCallback(({ data }: { data: any }) => {
    const clientTime = Date.now();
    if (data && !serverOffset) {
      const { clientRequestTime, serverResponseTime } = data ?? EMPTY_OBJECT;
      const RoundTripTime = clientTime - clientRequestTime;
      serverOffset = serverResponseTime + RoundTripTime / 2 - clientTime;
    }
  }, []);

  const onMessage = useCallback(
    (event: any) => {
      try {
        const parsed = JSON.parse(event?.data ?? '');
        const data = JSON.parse(parsed?.data);
        const channel = parsed?.channel;
        const channelType = getChannelType(channel);
        if (channel === pingChannel) {
          handleServerTimeOffset({ data });
        }
        handleEventEmitter(channelType, emitter, data);
        updateLastMessage(channel, data);
      } catch (error) {
        console.error('Error parsing message:', error);
        Analytics.track(ANALYTICS_EVENTS.WEB_SOCKET.ON_ERROR_IN_JSON_PARSE);
      }
    },
    [emitter, updateLastMessage, handleServerTimeOffset],
  );

  useEffect(() => {
    connect?.(session, onMessage);
    return () => {
      cleanUp?.();
    };
  }, [connect, session, cleanUp, onMessage]);

  useEffect(() => {
    joinChannel(pingChannel);
    if (pingPongTimeoutRef.current) {
      clearInterval(pingPongTimeoutRef.current);
    }
    if (_isEmpty(userId)) {
      return;
    }
    pingPongTimeoutRef.current = setInterval(() => {
      sendMessage({
        type: 'ping-pong',
        channel: pingChannel,
        data: {
          clientTime: Date.now(),
          currentActivity: getUserCurrentActivity({ currentUrl }),
          userId: _toString(userId),
        },
      });
    }, 5000);
    return () => {
      clearInterval(pingPongTimeoutRef.current);
      leaveChannel?.(pingChannel);
    };
  }, [userId, currentUrl, isConnected, joinChannel, sendMessage, leaveChannel]);

  return null;
};

export default useWebsocketInit;
