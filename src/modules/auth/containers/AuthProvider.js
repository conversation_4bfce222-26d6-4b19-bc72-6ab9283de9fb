/* eslint-disable react/function-component-definition */
import _isNil from 'lodash/isNil';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { useStorageState } from 'core/hooks/useStorageState';
import { Platform } from 'react-native';

import { useApolloClientContext } from 'core/contexts/apolloClientContext';
import _isEmpty from 'lodash/isEmpty';
import { useRouter } from 'expo-router';
import useRegisterPushNotificationToken from 'core/hooks/useRegisterPushNotificationToken';

import _pick from 'lodash/pick';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import {
  setUserAttributes,
  setUserIdentifier,
} from 'core/analytics/Crashlytics';
import { googleLogout } from '@react-oauth/google';
import Bugsnag from '@bugsnag/expo';
import useNetworkStatus from 'core/hooks/useNetworkStatus';
import usePrevious from 'core/hooks/usePrevious';
import useGetCurrentUserQuery from 'core/graphql/queries/useGetCurrentUser';
import _get from 'lodash/get';
import useDeleteUser from 'modules/profile/hooks/mutations/useDeleteUser';
import useCurrentUserCache from 'core/hooks/useCurrentUserCache';
import userReader from 'core/readers/userReader';
import useVerifyAuthToken from '../hooks/useVerifyAuthToken';
import useGuestLogin from '../hooks/useGuestLogin';
import useUpdateUserTimeZone from '../hooks/useUpdateUserTimeZone';
import useGetUpdatedUserStreaks from '../hooks/query/useGetUpdatedUserStreak';

const USER_PROPERTIES_TO_PICK = ['isGuest', 'rating'];

const AuthContext = React.createContext({
  signIn: () => null,
  signOut: () => null,
  session: null,
  isLoading: false,
  user: null,
  userId: null,
  isLoggedIn: false,
  isGuest: false,
  createGuestUser: () => null,
  refreshCurrentUser: () => null,
  updateCurrentUser: () => null,
  updateCurrentUserProperties: () => null,
  initialUrl: null,
  setInitialUrl: () => null,
  isReady: false,
});

// This hook can be used to access the user info.
export function useSession() {
  const value = React.useContext(AuthContext);
  if (process.env.NODE_ENV !== 'production') {
    if (!value) {
      throw new Error('useSession must be wrapped in a <SessionProvider />');
    }
  }

  return value;
}

let updatedUserTimeZone = false;
let isSignupEventTriggered = false;

export default function SessionProvider(props) {
  const { cachedUser, client } = props;
  const [session, setSession] = useStorageState('session');
  const { isNetworkReachable: isConnected } = useNetworkStatus();
  const [user, setUser] = useState(cachedUser);
  const [isSignUp, setIsSignUp] = useState(false);
  const { deleteUser } = useDeleteUser();
  const { setGuestId } = useGuestLogin();

  const { updateUserCache } = useCurrentUserCache();
  const [initialUrl, setInitialUrl] = useStorageState('initialUrl');
  const { updateUserTimeZone } = useUpdateUserTimeZone();
  const updateUserTimeZoneRef = useRef(updateUserTimeZone);
  const { fetchUserStreaks } = useGetUpdatedUserStreaks();
  const timeoutRef = useRef(null);
  updateUserTimeZoneRef.current = updateUserTimeZone;

  const { handleDeviceTokenUnRegistration } =
    useRegisterPushNotificationToken();
  const router = useRouter();

  const userId = userReader.id(user);

  const { fetchCurrentUser } = useGetCurrentUserQuery();

  const [isReady, setIsReady] = useState(!_isEmpty(cachedUser));
  const { getLoggedInUser } = useVerifyAuthToken();
  const { loginAsGuest } = useGuestLogin();

  const { updateApolloClient } = useApolloClientContext();

  const onUserSignIn = useCallback(
    async ({ user: loggedInUser }) => {
      webengage?.user?.login?.(loggedInUser?._id);
      const { token } = loggedInUser;
      await updateApolloClient(token).then(async () => {
        await setSession(token);
        setUser(loggedInUser);
      });
    },
    [setSession, updateApolloClient, setUser],
  );

  const signOut = useCallback(async () => {
    Analytics.logout();
    await handleDeviceTokenUnRegistration();
    if (Platform.OS === 'web') {
      googleLogout?.();
    } else {
      await GoogleSignin.signOut();
    }

    await setSession(null);
    setUser(undefined);
    await updateApolloClient(null);
    router.replace('/');
  }, [
    handleDeviceTokenUnRegistration,
    setSession,
    client,
    updateApolloClient,
    router,
  ]);

  const handleDeleteUser = useCallback(async () => {
    try {
      await deleteUser();
      signOut();
      setGuestId(null);
    } catch (error) {
      console.error('Error deleting user:', error);
    }
  }, [deleteUser, setGuestId, signOut]);

  const updateCrashlyticsUser = useCallback(async (userData) => {
    if (!userData) return;

    try {
      await setUserIdentifier(userData?._id ?? '');
      await setUserAttributes(userData);
    } catch (error) {
      console.error('Failed to update Crashlytics user:', error);
    }
  }, []);

  useEffect(() => {
    if (user) {
      updateCrashlyticsUser(user);
      Bugsnag.setUser(
        userReader.id(user),
        userReader.email(user),
        userReader.username(user),
      );
    }
  }, [user, updateCrashlyticsUser]);

  const updateCurrentUser = useCallback(
    (updatedUser) => {
      if (!_isEmpty(updatedUser)) {
        setUser((prevUser) => ({
          ...prevUser,
          ...updatedUser,
        }));
      }
    },
    [setUser],
  );

  const updateCurrentUserProperties = useCallback(
    (properties) => {
      if (!_isEmpty(properties)) {
        setUser((prevUser) => ({ ...prevUser, ...properties }));
      }
    },
    [setUser],
  );

  const refreshCurrentUser = useCallback(async () => {
    const response = await fetchCurrentUser();
    const updatedUser = _get(response, ['data', 'user']);
    const isDeletedAccount = userReader.isDeletedAccount(updatedUser);
    if (isDeletedAccount) {
      signOut();
    }
    updateCurrentUser(updatedUser);
  }, [fetchCurrentUser, signOut, updateCurrentUser]);

  const createGuestUser = useCallback(
    () =>
      loginAsGuest()
        .then(async (response) => {
          const { data } = response ?? EMPTY_OBJECT;
          const { guestUser } = data ?? EMPTY_OBJECT;
          const { token } = guestUser;
          Analytics.track(ANALYTICS_EVENTS.GUEST_LOGIN_SUCCESS, {
            userId: guestUser?._id,
            name: guestUser?.name,
          });

          await updateApolloClient(token);
          await setSession(token);
          setUser(guestUser);
          setIsReady(true);
          return guestUser;
        })
        .catch((error) => {
          Analytics.track(ANALYTICS_EVENTS.GUEST_LOGIN_FAILURE, { error });
        }),
    [loginAsGuest, updateApolloClient, setSession, setUser],
  );

  const authContextValue = useMemo(
    () => ({
      onUserSignIn,
      signOut,
      session,
      user,
      userId,
      isSignUp,
      isLoggedIn: _isNil(session),
      isLoading: false,
      createGuestUser,
      isReady,
      initialUrl,
      handleDeleteUser,
      setInitialUrl,
      updateCurrentUser,
      refreshCurrentUser,
      updateCurrentUserProperties,
    }),
    [
      onUserSignIn,
      signOut,
      session,
      isSignUp,
      createGuestUser,
      user,
      userId,
      isReady,
      initialUrl,
      handleDeleteUser,
      setInitialUrl,
      updateCurrentUser,
      refreshCurrentUser,
      updateCurrentUserProperties,
    ],
  );

  useEffect(() => {
    if (!_isEmpty(user)) {
      setIsReady(true);
    }
  }, [user]);

  const getLoggedInUserRef = useRef(getLoggedInUser);
  getLoggedInUserRef.current = getLoggedInUser;

  const userRef = useRef(user);
  userRef.current = user;

  const previousUserId = usePrevious(userReader.id(user));

  useEffect(() => {
    if (!_isNil(session)) {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      timeoutRef.current = setTimeout(async () => {
        const resp = await fetchUserStreaks();
        setUser((prevUser) => ({
          ...prevUser,
          userStreaks: {
            ..._get(prevUser, ['userStreaks'], EMPTY_OBJECT),
            ..._get(resp, ['data', 'getUpdatedUserStreaks'], EMPTY_OBJECT),
          },
        }));
      }, 1000);
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [session, fetchUserStreaks]);

  useEffect(() => {
    if (!_isNil(userId)) {
      Analytics.identify(userId);

      Analytics.setUserProperties({
        $email: userRef.current?.email,
        $name: userRef.current?.name,
        $avatar: userRef.current?.profileImageUrl,
        ..._pick(userRef.current, USER_PROPERTIES_TO_PICK),
        'global rank': userReader.globalRank(userRef.current),
        'games played': userReader.gamesPlayed(userRef.current),
        'current streak': userReader.currentStreak(userRef.current),
        'highest streak': userReader.longestStreak(userRef.current),
        username: userReader.username(userRef.current),
      });

      if (Platform.OS !== 'web') {
        webengage?.user?.setEmail?.(userRef.current?.email);
        webengage?.user?.setFirstName?.(userRef.current?.name);
      } else {
        // setting system attribute
        webengage?.user?.setAttribute?.('we_email', userRef.current?.email);
        webengage?.user?.setAttribute?.('we_first_name', userRef.current?.name);
        webengage?.user?.setAttribute?.(
          'we_avatar',
          userRef.current?.profileImageUrl,
        );
      }
    } else if (!_isNil(previousUserId)) {
      // user just logged out
      Analytics.logout();
    }
  }, [previousUserId, userId]);

  const updateUserCacheRef = useRef(updateUserCache);
  updateUserCacheRef.current = updateUserCache;

  useEffect(() => {
    (async () => {
      await updateUserCacheRef.current({ user });
    })();
    const isSignUp = userReader.isSignUp(user);
    setIsSignUp(isSignUp);

    if (isSignUp && !isSignupEventTriggered) {
      isSignupEventTriggered = true;
      Analytics.track('User Signup', { userId: userReader.id(user) });
    }

    if (!_isNil(user) && _isNil(user?.timezone) && !updatedUserTimeZone) {
      updatedUserTimeZone = true;
      updateUserTimeZoneRef.current();
    }
  }, [user]);

  const { children } = props;

  return (
    <AuthContext.Provider value={authContextValue}>
      {children}
    </AuthContext.Provider>
  );
}
