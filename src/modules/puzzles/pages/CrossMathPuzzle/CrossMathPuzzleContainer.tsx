import { useLocalSearchParams } from 'expo-router';
import React, { useEffect } from 'react';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { View } from 'react-native';
import CrossMathPuzzleHeader from 'modules/puzzles/components/CrossMathPuzzleHeader';
import ErrorView from 'atoms/ErrorView';
import { checkIsValidDate } from 'modules/puzzles/utils/puzzleUtils';
import ErrorBoundary from 'atoms/ErrorBoundary';
import CrossMathPuzzle from './CrossMathPuzzle';

const TRACKED_ATTEMPT_FOR_DATE = {};

const CrossMathPuzzleContainer = () => {
  const { date } = useLocalSearchParams();

  const isValidDate = checkIsValidDate(date);

  useEffect(() => {
    if (TRACKED_ATTEMPT_FOR_DATE[date]) return;
    TRACKED_ATTEMPT_FOR_DATE[date] = true;
    Analytics.track(
      ANALYTICS_EVENTS.CROSS_MATH_PUZZLE.ATTEMPTING_CROSS_MATH_PUZZLE,
      {
        date,
      },
    );
  }, [date]);

  if (!isValidDate) {
    return (
      <View style={{ flex: 1 }}>
        <CrossMathPuzzleHeader />
        <ErrorView errorMessage="Sorry, Puzzle is not available for selected date" />
      </View>
    );
  }

  return (
    <ErrorBoundary componentName="CrossMathPuzzleContainer">
      <CrossMathPuzzle date={date} />
    </ErrorBoundary>
  );
};

CrossMathPuzzleContainer.displayName = 'CrossMathPuzzleContainer';

export default React.memo(CrossMathPuzzleContainer);
