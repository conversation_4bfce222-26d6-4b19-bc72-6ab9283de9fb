import React from 'react';
import Loading from '@/src/components/atoms/Loading';
import { View } from 'react-native';
import Header from '@/src/components/shared/Header';
import ShowdownGameLobbyPlayerCards from '../../components/ShowdownGameLobbyPlayerCards';
import styles from './style';
import useShowdownStore from '@/src/store/useShowdownStore';

const ShowdownLobby = () => {
  const { loading } = useShowdownStore((state) => ({
    loading: state.showdownLoading,
  }));

  if (loading) {
    return <Loading />;
  }

  return (
    <View style={styles.lobbyContainer}>
      <Header title="Showdown Game" />
      <View style={styles.container}>
        <View style={styles.container}>
          <View style={styles.contentContainer}>
            <ShowdownGameLobbyPlayerCards />
          </View>
        </View>
      </View>
    </View>
  );
};

export default React.memo(ShowdownLobby);
