import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import _size from 'lodash/size';

import { useSession } from '@/src/modules/auth/containers/AuthProvider';

import _includes from 'lodash/includes';
import _map from 'lodash/map';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import useShowdownLifeCycle from '@/src/modules/showdownV2/hooks/useShowdownLifeCycle';
import _debounce from 'lodash/debounce';
import useJoinGameQuery from '../../hooks/useJoinGameQuery';
import { GAME_STATUS } from '../../constants/game';
import ShowdownPlayGame from '../ShowdownPlayGame';
import GameFullPage from '../GameFullPage/GameFullPage';
import ShowdownLobby from './ShowdownLobby';

const COMPONENT_FACTORY = {
  [GAME_STATUS.CREATED]: ShowdownLobby,
  [GAME_STATUS.READY]: ShowdownLobby,
  [GAME_STATUS.STARTED]: ShowdownPlayGame,
  [GAME_STATUS.ENDED]: ShowdownPlayGame,
};

const ShowdownGame = (props: { game: any }) => {
  const { game } = props;
  const { userId } = useSession();
  const { joinGame } = useJoinGameQuery();

  const {
    players,
    gameStatus,
    _id: gameId,
    showdownId,
    config,
  } = game ?? EMPTY_OBJECT;

  useShowdownLifeCycle(showdownId);

  const { numPlayers } = config;

  const execJoinGame = useCallback(async () => {
    await joinGame({ gameId }).then(() => {
      Analytics.track(ANALYTICS_EVENTS.SHOWDOWN_JOIN_GAME, { gameId });
    });
  }, [gameId, joinGame]);

  const execJoinRef = useRef(execJoinGame);
  execJoinRef.current = execJoinGame;

  const debouncedJoinGame = useRef(
    _debounce(() => {
      execJoinRef.current();
    }, 500),
  );

  const shouldCallJoinGame = useMemo(() => {
    const playerStatus = (players ?? [])?.find(
      (player: any) => player.userId === userId,
    )?.status;
    if (gameStatus === GAME_STATUS.CREATED && playerStatus === 'INVITED') {
      return true;
    }
    return false;
  }, [players, userId, gameStatus]);

  useEffect(() => {
    if (shouldCallJoinGame) {
      debouncedJoinGame.current();
    }
  }, [shouldCallJoinGame]);

  if (
    _size(players) === numPlayers &&
    !_includes(_map(players, 'userId'), userId)
  ) {
    return <GameFullPage />;
  }

  const Component = COMPONENT_FACTORY[gameStatus];

  if (!Component) {
    return null;
  }

  return <Component game={game} />;
};

export default React.memo(ShowdownGame);
