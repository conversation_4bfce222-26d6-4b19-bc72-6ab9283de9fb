import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Platform, View } from 'react-native';
import usePrevious from '@/src/core/hooks/usePrevious';
import useMediaQuery from 'core/hooks/useMediaQuery';
import Dark from '@/src/core/constants/themes/dark';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { KEYBOARD_TYPES } from 'shared/CustomKeyboard';
import { GAME_TYPES } from 'core/constants/gameTypes';
import _isNil from 'lodash/isNil';
import _isEmpty from 'lodash/isEmpty';
import CustomTextInput from 'shared/CustomTextInput';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import { checkAnswer } from '@/src/wasm';
import styles from './Footer.style';
import { evaluateAnswer, SOLUTION_STATUS } from './AnswerEvaluators';

const Footer = ({
  question = EMPTY_OBJECT,
  gameType,
  submitAnswer,
  renderInputBox = true,
  isGameActive = true,
}) => {
  const [value, setValue] = useState('');
  const [isIncorrect, setIsIncorrect] = useState(false);
  const [startTime, setStartTime] = useState(0);
  const [solutionStatus, setSolutionStatus] = useState(
    SOLUTION_STATUS.NOT_ANSWERED,
  );

  const { id: questionId, answers, tags, expression, category } = question;

  const validAnswer = answers?.[0];
  const previousQuestionId = usePrevious(questionId);
  const inputRef = useRef(null);

  const { isMobile } = useMediaQuery();

  const focusInput = useCallback(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, [inputRef]);

  useEffect(() => {
    setSolutionStatus(null);
  }, [question]);

  const onChangeText = useCallback(
    (inputText) => {
      if (!isGameActive) return;
      setValue(inputText);
      if (!inputText || _isEmpty(questionId)) return;
      let result = {};

      const currTime = getCurrentTimeWithOffset();
      const category = tags?.[0];
      if (gameType === GAME_TYPES.PLAY_ONLINE && Platform.OS === 'web') {
        if (
          !question?.answerLength ||
          question.answerLength < 1 ||
          question.answerLength !== inputText.length
        ) {
          return;
        }

        const check = checkAnswer(questionId, inputText);
        result = {
          isCorrect: check,
          shouldCheck: !check,
          processedValue: inputText,
        };
      } else {
        result = evaluateAnswer({
          category,
          text: inputText,
          validAnswer: answers,
          expression,
        });
      }

      if (result.isCorrect) {
        setSolutionStatus(SOLUTION_STATUS.CORRECT);
        submitAnswer({
          questionId,
          value: result.processedValue,
          timeTaken: currTime - startTime,
          isCorrect: result.isCorrect,
          isCheckedWithWasm:
            Platform.OS === 'web' && gameType === GAME_TYPES.PLAY_ONLINE,
        });
      } else if (result.shouldCheck) {
        setSolutionStatus(SOLUTION_STATUS.INCORRECT);
        submitAnswer({
          questionId,
          value: result.processedValue,
          timeTaken: currTime - startTime,
          isCorrect: result.isCorrect,
          isCheckedWithWasm:
            Platform.OS === 'web' && gameType === GAME_TYPES.PLAY_ONLINE,
        });
      } else {
        setSolutionStatus(null);
      }
    },
    [
      isGameActive,
      questionId,
      tags,
      gameType,
      question.answerLength,
      answers,
      expression,
      submitAnswer,
      startTime,
    ],
  );

  useEffect(() => {
    if (
      isGameActive &&
      previousQuestionId !== questionId &&
      !_isNil(questionId)
    ) {
      setValue('');
      setStartTime(getCurrentTimeWithOffset());
      setIsIncorrect(false);
      requestAnimationFrame(() => {
        focusInput();
      });
    }
  }, [previousQuestionId, questionId, focusInput, isGameActive]);

  useEffect(() => {
    if (isGameActive) {
      requestAnimationFrame(() => {
        focusInput();
      });
    }
  }, [focusInput, isGameActive]);

  const onPressClearAll = useCallback(() => {
    onChangeText('');
    focusInput();
  }, [onChangeText, focusInput]);

  const inputProps = useMemo(
    () => ({
      value,
      style: isMobile ? styles.inputStyle : styles.inputStyleDesktop,
      placeholder: 'Enter Answer',
      keyboardAppearance: 'dark',
      placeholderTextColor: '#777',
      onChangeText,
      answer: validAnswer,
      keyboardType: 'number-pad',
      autoFocus: true,
      onFocus: focusInput,
      solutionStatus,
      customKeyboardType:
        gameType === GAME_TYPES.ABILITY_DUELS ||
        gameType === GAME_TYPES.DMAS_ABILITY
          ? KEYBOARD_TYPES.NUMBERS_AND_OPERATORS
          : KEYBOARD_TYPES.NUMBERS,
      customKeyboard: isMobile,
      onClearAll: onPressClearAll,
    }),
    [
      value,
      isMobile,
      onChangeText,
      validAnswer,
      focusInput,
      solutionStatus,
      gameType,
      onPressClearAll,
    ],
  );

  const shouldShowSuccessIcon = useMemo(
    () =>
      gameType === GAME_TYPES.FASTEST_FINGER &&
      !isMobile &&
      solutionStatus === SOLUTION_STATUS.CORRECT,
    [gameType, isMobile, solutionStatus],
  );

  return (
    <View style={styles.container}>
      <CustomTextInput {...inputProps} />
      {shouldShowSuccessIcon && (
        <View style={styles.errorContainer}>
          <View style={styles.closeIconContainer}>
            <FontAwesome name="check" size={20} color={Dark.colors.success} />
          </View>
        </View>
      )}
    </View>
  );
};

export default React.memo(Footer);
