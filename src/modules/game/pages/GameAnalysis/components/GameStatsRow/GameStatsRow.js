import React from 'react';
import { Text, View } from 'react-native';
import PropTypes from 'prop-types';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import styles from './GameStatsRow.style';
import dark from '../../../../../../core/constants/themes/dark';
import { getValueText } from '../../../../utils/getComplementText';
import _isNil from 'lodash/isNil'

export const isFaster = ({ userScore, opponentScore }) => {
  if(_isNil(userScore) && _isNil(opponentScore)){
    return false; 
  }

  if(_isNil(opponentScore)){
    return !_isNil(userScore)
  }

  if(_isNil(userScore)){
    return false;
  }

  return userScore < opponentScore
}


const GameStatsRow = (props) => {
  const { userScore, opponentScore, fieldName } = props;
  const isUserBetterThanOpponent = isFaster({ userScore, opponentScore });
  const shouldShowArrow = !(_isNil(userScore) && _isNil(opponentScore));

  return (
    <View style={styles.container}>
      <View
        style={[
          styles.userContainer,
          shouldShowArrow && isUserBetterThanOpponent
            ? { borderColor: dark.colors.tertiary }
            : { borderColor: 'transparent' },
        ]}
      >
        <Text style={[styles.currUserScore]}>{getValueText(userScore)}</Text>
      </View>
      <View style={styles.statsFieldContainer}>
        {shouldShowArrow && isUserBetterThanOpponent ? (
          <MaterialIcons
            name="arrow-back-ios"
            color={dark.colors.textLight}
            size={10}
          />
        ) : (
          <View style={styles.emptyIcon} />
        )}

        <Text style={styles.statsField}>{fieldName}</Text>
        {shouldShowArrow && !isUserBetterThanOpponent ? (
          <MaterialIcons
            name="arrow-forward-ios"
            color={dark.colors.textLight}
            size={10}
          />
        ) : (
          <View style={styles.emptyIcon} />
        )}
      </View>
      <View
        style={[
          styles.opponentContainer,
          shouldShowArrow && !isUserBetterThanOpponent
            ? { borderColor: dark.colors.tertiary }
            : { borderColor: 'transparent' },
        ]}
      >
        <Text style={[styles.opponentUserScore]}>
          {getValueText(opponentScore)}
        </Text>
      </View>
    </View>
  );
};

GameStatsRow.propTypes = {
  userScore: PropTypes.number,
  opponentScore: PropTypes.number,
  fieldName: PropTypes.string,
};

export default React.memo(GameStatsRow);
