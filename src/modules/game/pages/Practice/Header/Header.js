import React, { useCallback, useEffect, useRef, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { Text } from '@rneui/themed';

import _toNumber from 'lodash/toNumber';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import Dark from 'core/constants/themes/dark';
import gameReader from 'core/readers/gameReader';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import useGameContext from '../../../hooks/useGameContext';

const styles = StyleSheet.create({
  container: {
    justifyContent: 'space-between',
    paddingVertical: 8,
    alignItems: 'center',
    gap: 8,
    backgroundColor: Dark.colors.background,
  },
  timerBox: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    width: 100,
    justifyContent: 'center',
  },
  timerText: {
    fontSize: 16,
    fontFamily: 'Montserrat-700',
    color: 'white',
  },
  playerScore: {
    color: Dark.colors.success,
    fontSize: 22,
  },
});

const Header = ({ playerScore, onGameEnded }) => {
  const { game } = useGameContext();
  const { startTime } = game;

  const timeLimit = gameReader.timeLimit(game);

  const startTimeDate = new Date(startTime);
  const currentTime = getCurrentTimeWithOffset();

  const endTime = startTimeDate.getTime() + _toNumber(timeLimit) * 1000;
  const timeLeftInSecond = Math.max(
    Math.ceil((endTime - currentTime) / 1000),
    0,
  );

  const [timer, setTimer] = useState(timeLeftInSecond);

  const formatTime = useCallback((seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  }, []);

  const currTimeRef = useRef();

  const renderScore = useCallback(
    () => <Text style={styles.playerScore}>{playerScore}</Text>,
    [playerScore],
  );

  useEffect(() => {
    if (currTimeRef.current) {
      clearInterval(currTimeRef.current);
    }

    currTimeRef.current = setInterval(() => {
      setTimer((prevTimer) => Math.max(prevTimer - 1, 0));
    }, 1000);

    return () => clearInterval(currTimeRef.current);
  }, [timer]);

  const onGameEndedRef = useRef(onGameEnded);
  onGameEndedRef.current = onGameEnded;

  useEffect(() => {
    if (timer <= 0) {
      onGameEndedRef?.current?.();
    }
  }, [timer]);

  return (
    <View style={styles.container}>
      <View style={styles.timerBox}>
        <MaterialIcons name="timer" color="white" size={20} />
        <Text style={styles.timerText}> {formatTime(timer)}</Text>
      </View>
      {renderScore()}
    </View>
  );
};

export default Header;
