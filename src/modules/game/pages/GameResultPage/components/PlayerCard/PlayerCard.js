import React, { useCallback } from 'react';
import { Image, Pressable, Text, View } from 'react-native';
import rec1 from '@/assets/images/Rectangle5.png';
import rec2 from '@/assets/images/Rectangle6.png';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import UserImage from 'atoms/UserImage/UserImage';
import _isEmpty from 'lodash/isEmpty';
import userReader from '@/src/core/readers/userReader';
import { GAME_TYPES } from '@/src/modules/home/<USER>/gameTypes';
import _truncate from 'lodash/truncate';
import { useRouter } from 'expo-router';
import { closePopover } from 'molecules/Popover/Popover';
import useMediaQuery from 'core/hooks/useMediaQuery';
import styles from './PlayerCard.style';

const DEFAULT_RATING = 1000;

const PlayerCard = (props) => {
  const router = useRouter();
  const { user, gameType } = props;
  const { user: currentUser } = useSession();
  const isCurrentUser = user?._id === currentUser?._id;
  const { score, isWinner } = user;

  const { isMobile } = useMediaQuery();

  const truncatedUsername = _truncate(userReader.username(user), {
    length: isMobile ? 10 : 12,
    omission: '...',
  });

  let userRating = userReader.rating(user);

  if (gameType === GAME_TYPES.FLASH_ANZAN) {
    userRating = userReader.flashAnzanRating(user) ?? DEFAULT_RATING;
  } else if (
    gameType === GAME_TYPES.ABILITY_DUELS ||
    gameType === GAME_TYPES.DMAS_ABILITY
  ) {
    userRating = userReader.abilityDuelsRating(user) ?? DEFAULT_RATING;
  }

  const onPressUserCard = useCallback(() => {
    const username = userReader.username(user);
    closePopover();
    router.navigate(`/profile/${username}`);
  }, [user, router]);

  if (_isEmpty(user)) {
    return <View />;
  }

  return (
    <Pressable onPress={onPressUserCard}>
      <View
        style={[
          styles.background,
          isWinner
            ? isCurrentUser
              ? styles.winnerContainer
              : styles.loserContainer
            : styles.defaultContainer,
        ]}
      >
        <View style={{ borderRadius: 14, minHeight: 108 }}>
          <Image
            source={rec1}
            style={[styles.image1, !isCurrentUser && { opacity: 0 }]}
          />
          <Image
            source={rec2}
            style={[styles.image2, !isCurrentUser && { opacity: 0 }]}
          />
          <View style={styles.card}>
            <UserImage size={34} user={user} />
            <View style={styles.userInfo}>
              <Text style={styles.userName}>{truncatedUsername}</Text>
              <Text style={styles.userScore}>({userRating})</Text>
            </View>
          </View>
        </View>
      </View>
    </Pressable>
  );
};

export default React.memo(PlayerCard);
