import React, { useCallback, useState } from 'react';
import { Dimensions, Text, View } from 'react-native';
import PropTypes from 'prop-types';
import Loading from 'atoms/Loading';
import ErrorView from 'atoms/ErrorView';
import PrimaryButton from 'atoms/PrimaryButton';
import SecondaryButton from 'atoms/SecondaryButton';
import UserImage from 'atoms/UserImage';
import useMediaQuery from 'core/hooks/useMediaQuery';
import Header from 'shared/Header';
import _map from 'lodash/map';
import { useLocalSearchParams } from 'expo-router';
import useGameConfig from '../../../home/<USER>/useGameConfig';
import userReader from '../../../../core/readers/userReader';
import { GAME_CONFIGS } from '../../../home/<USER>/gameConfig';
import useChallengeUser from '../../../friendsAndFollowers/hooks/mutations/useChallengeUser';
import { GAME_TYPES } from '../../../home/<USER>/gameTypes';

import useGetUserByUserNameQuery from '../../../../core/hooks/useGetUserByUsername';
import { useSession } from '../../../auth/containers/AuthProvider';
import styles from './CustomChallenge.style';

const CustomChallenge = (props) => {
  const { gameConfig: selectedGameConfig, updateGameConfig } = useGameConfig();

  const { gameType } = useLocalSearchParams();
  const { opponentUser } = props;
  const { user: currUser } = useSession();

  const { isCompactMode } = useMediaQuery();

  const [isChallengingFriend, setIsChallengingFriend] = useState(false);

  const { challengeUser } = useChallengeUser();

  const onPressSendChallenge = useCallback(async () => {
    try {
      if (isChallengingFriend) {
        return;
      }
      const playTime = selectedGameConfig.PLAY_TIME;
      const gameConfig = {
        timeLimit: playTime * 60,
        numPlayers: 2,
        gameType: gameType ?? GAME_TYPES.ONLINE_CHALLENGE,
      };
      setIsChallengingFriend(true);

      await challengeUser({
        userId: opponentUser?._id,
        gameConfig,
      });
      setIsChallengingFriend(false);
    } catch (e) {
      setIsChallengingFriend(false);
    } finally {
      setIsChallengingFriend(false);
    }
  }, [
    challengeUser,
    opponentUser?._id,
    selectedGameConfig,
    isChallengingFriend,
    gameType,
  ]);

  const renderGameConfigOption = useCallback(
    ({ option, gameConfig }) => {
      const { key: configKey } = gameConfig;
      const { key, label } = option;
      const isSelected = selectedGameConfig[configKey] === key;

      return (
        <SecondaryButton
          key={key}
          radius={6}
          label={label}
          onPress={() => updateGameConfig({ key: configKey, value: key })}
          containerStyle={[
            styles.buttonContainer,
            isCompactMode && styles.compactButtonContainer,
          ]}
          buttonStyle={[
            styles.timeContainer,
            isSelected && { borderColor: 'white' },
          ]}
          labelStyle={styles.buttonLabelStyle}
        />
      );
    },
    [updateGameConfig, selectedGameConfig],
  );

  const renderGameConfig = useCallback(
    (gameConfig) => {
      const { key, label, options } = gameConfig;

      return (
        <View key={key} style={styles.gameConfigContainer}>
          <View style={styles.configOptionsContainer}>
            {_map(options, (option) =>
              renderGameConfigOption({ option, gameConfig }),
            )}
          </View>
        </View>
      );
    },
    [renderGameConfigOption],
  );

  return (
    <View style={styles.container}>
      <Header title="Create Challenge" />
      <View style={styles.innerContainer}>
        <View style={styles.optionsSection}>
          <View style={styles.usersInfoRow}>
            <View style={styles.userInfo}>
              <UserImage
                user={currUser}
                rounded={false}
                style={styles.userImage}
              />
              <Text style={styles.userName} numberOfLines={1}>
                You
              </Text>
              <Text style={styles.rating}>{currUser?.rating}</Text>
            </View>
            <Text style={styles.vsText}>VS</Text>
            <View style={styles.userInfo}>
              <UserImage
                user={opponentUser}
                rounded={false}
                style={styles.userImage}
              />
              <Text style={styles.userName} numberOfLines={1}>
                {userReader.username(opponentUser)}
              </Text>
              <Text style={styles.rating}>{opponentUser?.rating}</Text>
            </View>
          </View>
          <Text style={styles.sprintDuelText}>SPRINT DUEL</Text>
          <View style={styles.configOptionsContainer}>
            {_map(GAME_CONFIGS, (config) => renderGameConfig(config))}
          </View>
        </View>
      </View>

      <View style={styles.challengeButton}>
        <PrimaryButton
          onPress={onPressSendChallenge}
          label={isChallengingFriend ? 'Challenging.....' : 'Send Challenge'}
          radius={20}
          buttonStyle={{
            height: 40,
            width: Dimensions.get('window').width - 32,
          }}
        />
      </View>
    </View>
  );
};

CustomChallenge.propTypes = {
  opponentUser: PropTypes.object,
};

const CustomChallengeContainer = (props) => {
  const { username } = props;
  const { user, loading, error } = useGetUserByUserNameQuery({
    userName: username,
  });

  if (loading) {
    return <Loading label="Loading...." />;
  }

  if (error) {
    return (
      <ErrorView errorMessage={`User not found with username ${username}`} />
    );
  }

  return <CustomChallenge opponentUser={user} />;
};

CustomChallengeContainer.propTypes = {
  username: PropTypes.string,
};

export default React.memo(CustomChallengeContainer);
