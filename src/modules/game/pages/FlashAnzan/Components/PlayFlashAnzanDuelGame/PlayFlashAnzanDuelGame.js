import React, { useRef } from 'react';
import { Text, View } from 'react-native';
import CustomTextInput from 'shared/CustomTextInput';
import useMediaQuery from 'core/hooks/useMediaQuery';
import dark from 'core/constants/themes/dark';
import useHandleFlashAnzanPlay from '@/src/modules/game/hooks/useHandleFlashAnzanPlay';
import FlashAnzanConfigSelection from '@/src/modules/game/pages/FlashAnzan/Components/FlashAnzanConfigSelection';
import FLASH_ANZAN_GAME_PHASES from '@/src/modules/game/constants/flashAnzanGamePhases';
import Loading from 'atoms/Loading';
import FlashAnzanScoreHeader from '../FlashAnzanScoreHeader';
import useFlashAnzanPlayPageStyles from './PlayFlashAnzanDuelGame.style';
import FlashNumber from './FlashNumber';

const PlayFlashAnzanDuelGame = (props) => {
  const { game } = props;
  const {
    startTime,
    config: gameConfig,
    gameType,
    _id: gameId,
    gameStatus,
  } = game;

  const styles = useFlashAnzanPlayPageStyles();

  const { isMobile: isCompactMode, isMobileBrowser } = useMediaQuery();

  const gameTypeRef = useRef(gameType);
  gameTypeRef.current = gameType;
  const configRef = useRef(gameConfig);
  configRef.current = gameConfig;

  const {
    config,
    gamePhase,
    isCorrect,
    isFlashing,
    isInputWrong,
    userAnswer,
    currentQuestion,
    currentNumberIndex,
    numbers,
    phaseCountdown,

    setUserAnswer,
    updateConfig,
    solutionStatus,
  } = useHandleFlashAnzanPlay();

  const { noOfQuestions } = config;

  const inputProps = {
    value: userAnswer,
    style: [
      styles.input,
      isInputWrong && { borderColor: dark.colors.errorDark },
      isCorrect && { borderColor: dark.colors.primary1 },
    ],
    keyboardAppearance: 'dark',
    placeholderTextColor: dark.colors.inputPlaceholder,
    solutionStatus,
    customKeyboard: isCompactMode,
    onChangeText: setUserAnswer,
    keyboardType: 'number-pad',
    contextMenuHidden: true,
    autoCorrect: false,
    blurOnSubmit: false,
    autoFocus: true,
    editable: isMobileBrowser ? false : !isCorrect,
  };

  const renderStartingCountdown = () => (
    <View style={styles.countdownContainer}>
      <Text style={styles.countdownText}>Starting in {phaseCountdown}</Text>
    </View>
  );

  const renderConfigSelection = () => (
    <View style={styles.configContainer}>
      <Text style={styles.questionText}>QUESTION {currentQuestion}</Text>
      <Text style={styles.countdownText}>Starts in {phaseCountdown}</Text>
      <FlashAnzanConfigSelection
        selectedConfig={config}
        updateConfig={updateConfig}
      />
    </View>
  );

  const renderFlashingSection = () => (
    <FlashNumber number={numbers[currentNumberIndex]} />
  );

  const renderAnsweringSection = () => (
    <View style={[styles.answerContainer, !isCompactMode && { width: '40%' }]}>
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          maxHeight: 400,
        }}
      >
        <Text style={styles.countdown}>
          {currentQuestion < noOfQuestions ? 'Next Question in' : 'Results in '}
        </Text>
        <Text style={styles.countdownTime}>{phaseCountdown}</Text>
      </View>
      <View style={styles.textInputRow}>
        <CustomTextInput {...inputProps} />
      </View>
    </View>
  );

  const renderGameContent = () => {
    switch (gamePhase) {
      case FLASH_ANZAN_GAME_PHASES.CONFIG:
        return renderConfigSelection();
      case FLASH_ANZAN_GAME_PHASES.STARTING:
        return renderStartingCountdown();
      case FLASH_ANZAN_GAME_PHASES.FLASHING:
        return renderFlashingSection();
      case FLASH_ANZAN_GAME_PHASES.ANSWERING:
        return renderAnsweringSection();
      case FLASH_ANZAN_GAME_PHASES.COMPLETED:
        return <Loading label="Waiting for opponent to Finish Game" />;
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <FlashAnzanScoreHeader currentQuestion={currentQuestion} />
      </View>
      <View
        style={[
          styles.innerContainer,
          (isFlashing || !isMobileBrowser) && { justifyContent: 'center' },
        ]}
      >
        {renderGameContent()}
      </View>
    </View>
  );
};

export default React.memo(PlayFlashAnzanDuelGame);
