import React, { useCallback, useEffect, useState } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import Entypo from '@expo/vector-icons/Entypo';
import AntDesign from '@expo/vector-icons/AntDesign';
import dark from 'core/constants/themes/dark';
import UserImage from 'atoms/UserImage/UserImage';
import { ChallengeUserPayloadType } from 'core/types/challengeUserPayload';
import { User } from 'core/types/userTypes';
import challengeUserPayloadReader from 'core/readers/challengeUserPayloadReader';
import { GAME_TYPES } from 'core/constants/gameTypes';
import _toNumber from 'lodash/toNumber';
import { PUZZLE_GAME_TYPES } from 'modules/home/<USER>/puzzleGameTypes';
import _includes from 'lodash/includes';
import useAcceptChallengeRequestOfPuzzleGame from 'modules/puzzleGame/hooks/mutations/useAcceptChallengeOfPuzzleGame';
import useRejectChallengeRequestOfPuzzleGame from 'modules/puzzleGame/hooks/mutations/useRejectChallengeOfPuzzleGame';
import { PUZZLE_KEY } from '@/src/overlays/hooks/useChallengeUserEvents';
import userReader from 'core/readers/userReader';
import useAcceptChallengeRequest from '../../../hooks/mutations/useAcceptChallenge';
import useRejectChallengeRequest from '../../../hooks/mutations/useRejectChallenge';
import useChallengeRequestCardStyles from './ChallengeRequestCard.style';

const GAME_TYPES_VS_LABEL = {
  [GAME_TYPES.ONLINE_CHALLENGE]: 'Duel',
  [GAME_TYPES.DMAS]: 'Duel',
  [GAME_TYPES.FLASH_ANZAN]: 'Flash Anzan',
  [GAME_TYPES.FASTEST_FINGER]: 'Fastest Finger',
  [GAME_TYPES.ABILITY_DUELS]: 'Ability Duels',
  [GAME_TYPES.DMAS_ABILITY]: 'Ability Duels',
  [PUZZLE_GAME_TYPES.CROSS_MATH_PUZZLE_WITH_FRIEND]: 'Puzzle Duel',
};

const useGetAcceptAndRejectAccToGameType = (gameType: string) => {
  const { acceptChallengeRequestOfPuzzleGame } =
    useAcceptChallengeRequestOfPuzzleGame();
  const { rejectChallengeRequestOfPuzzleGame } =
    useRejectChallengeRequestOfPuzzleGame();
  const { acceptChallengeRequest } = useAcceptChallengeRequest();
  const { rejectChallengeRequest } = useRejectChallengeRequest();

  if (_includes(gameType, PUZZLE_KEY)) {
    return {
      acceptChallengeRequest: acceptChallengeRequestOfPuzzleGame,
      rejectChallengeRequest: rejectChallengeRequestOfPuzzleGame,
    };
  }

  return {
    acceptChallengeRequest,
    rejectChallengeRequest,
  };
};

const ChallengeRequestCard = ({
  user,
  payload,
}: {
  user: User;
  payload: ChallengeUserPayloadType;
}) => {
  const waitingTime = 20;
  const gameId = challengeUserPayloadReader.gameId(payload);
  const gameConfig = challengeUserPayloadReader.gameConfig(payload);

  const gameType = gameConfig?.gameType;
  const gameTimeLimitInMinutes = Math.floor(
    _toNumber(gameConfig?.timeLimit) / 60,
  );
  const { acceptChallengeRequest, rejectChallengeRequest } =
    useGetAcceptAndRejectAccToGameType(gameType);

  const gameTypeLabel = GAME_TYPES_VS_LABEL[gameType] ?? 'Duel';

  const styles = useChallengeRequestCardStyles();

  const [isVisible, setIsVisible] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [timeLeft, setTimeLeft] = useState(Math.floor(waitingTime));

  useEffect(() => {
    const timerInterval = setInterval(() => {
      setTimeLeft((prevTime) => {
        if (prevTime <= 1) {
          clearInterval(timerInterval);
          setIsVisible(false);
        }
        return prevTime - 1;
      });
    }, 1000);

    return () => clearInterval(timerInterval);
  }, [waitingTime]);

  const handleAccept = useCallback(async () => {
    if (!isLoading) {
      setIsLoading(true);
      try {
        await acceptChallengeRequest({ gameId });
      } catch (error) {
        console.error(error);
      } finally {
        setIsLoading(false);
      }
    }
  }, [acceptChallengeRequest, gameId, isLoading]);

  const handleReject = useCallback(async () => {
    if (!isLoading) {
      setIsLoading(true);
      try {
        await rejectChallengeRequest({ gameId });
      } catch (error) {
        console.error(error);
      } finally {
        setIsLoading(false);
      }
    }
  }, [rejectChallengeRequest, gameId, isLoading]);

  if (!isVisible) return null;

  return (
    <View style={styles.requestCard}>
      <View style={styles.container}>
        <UserImage size={34} user={user} style={styles.image} rounded={false} />
        <View style={{ width: 260 }}>
          <Text
            style={{
              color: 'white',
              fontFamily: 'Montserrat-500',
              fontSize: 12,
            }}
          >
            {`${userReader.username(user)}(${userReader.rating(user)}) has challenged you for ${gameTimeLimitInMinutes} Minute ${gameTypeLabel} match.`}
          </Text>
        </View>
      </View>
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.button}
          onPress={handleReject}
          disabled={isLoading}
        >
          <Entypo name="cross" size={20} color={dark.colors.error} />
          <Text style={styles.rejectText}>Reject</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.button}
          onPress={handleAccept}
          disabled={isLoading}
        >
          <AntDesign name="check" size={20} color={dark.colors.success} />
          <Text style={styles.acceptText}>
            {isLoading ? 'Processing...' : `Accept (${timeLeft}s)`}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default ChallengeRequestCard;
