import React, { useCallback } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import PropTypes from 'prop-types';
import UserImage from 'atoms/UserImage';
import { useRouter } from 'expo-router';
import _isEqual from 'lodash/isEqual';
import { useSession } from 'modules/auth/containers/AuthProvider';
import useJoinedPlayersCardStyles from './JoinedPlayersCard.style';
import useRemovePlayerFromGame from '../../../../hooks/mutations/useRemovePlayer';

const JoinedPlayersCard = (props) => {
  const styles = useJoinedPlayersCardStyles();
  const { userId } = useSession();
  const { userDetailsData, gameId, creatorId, isAdmin } = props;
  const router = useRouter();
  const isCurrentUserAdmin = _isEqual(creatorId, userId);

  const {
    username,
    profileImageUrl,
    rating,
    _id: playerId,
  } = userDetailsData ?? EMPTY_OBJECT;
  const { removePlayerFromGame, removingPlayer } = useRemovePlayerFromGame();

  const handleRemovePlayerFromGame = useCallback(async () => {
    if (!isCurrentUserAdmin) {
      return;
    }
    await removePlayerFromGame({ gameId, playerId });
  }, [gameId, removePlayerFromGame, playerId, isCurrentUserAdmin]);

  const navigateToUserProfile = useCallback(() => {
    router.push(`/profile/${username}`);
  }, [username, router]);

  return (
    <TouchableOpacity style={styles.container} onPress={navigateToUserProfile}>
      <View style={styles.userInfoWithImage}>
        <UserImage
          style={styles.userImage}
          user={{ profileImageUrl }}
          rounded={false}
        />
        <View style={styles.userInfo}>
          <Text style={styles.userName}>
            {username} {isAdmin && '(admin)'}
          </Text>
          <Text style={styles.userRating}>{rating}</Text>
        </View>
      </View>
      {isCurrentUserAdmin && (
        <View style={[styles.userInfoWithImage, { alignItems: 'center' }]}>
          {!isAdmin && (
            <TouchableOpacity onPress={handleRemovePlayerFromGame}>
              <Text style={styles.challengeText}>
                {removingPlayer ? 'Removing....' : 'Remove'}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      )}
    </TouchableOpacity>
  );
};

JoinedPlayersCard.propTypes = {
  userDetailsData: PropTypes.object,
  gameId: PropTypes.string,
  isAdmin: PropTypes.bool,
  creatorId: PropTypes.string,
};

export default React.memo(JoinedPlayersCard);
