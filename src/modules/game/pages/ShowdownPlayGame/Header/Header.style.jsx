import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    maxWidth: 420,
    width: '100%',
    // backgroundColor: "#000",
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
    height: 54,
    gap: 16,
  },
  containerMob: {
    // borderWidth: 0.5,
    // width: '90%',
    width: '100%',
    // borderColor: '#666',
    // borderRadius: 12,
    overflow: 'hidden',
    height: 85,
    paddingHorizontal: 4,
  },
  userPoints: {
    color: dark.colors.textDark,
    fontSize: 12,
    lineHeight: 14.6,
    marginBottom: 6,
  },
  timerContainer: {
    alignItems: 'center',
  },
  timerBox: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    width: 90,
    justifyContent: 'center',
  },
  timerText: {
    marginBottom: -1,
    fontSize: 18,
    fontFamily: 'Montserrat-700',
    color: dark.colors.textDark,
  },
  timerText2: {
    marginBottom: -1,
    fontSize: 14,
    fontFamily: 'Montserrat-700',
    color: '#FFFFFF',
  },
  backImg: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    resizeMode: 'stretch',
  },
});

export default styles;
