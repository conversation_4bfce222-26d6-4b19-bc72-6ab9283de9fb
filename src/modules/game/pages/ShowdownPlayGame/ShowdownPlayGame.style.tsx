import { StyleSheet } from 'react-native';
import Dark from '@/src/core/constants/themes/dark';

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Dark.colors.background,
    width: '100%',
  },
  mainContainerWeb: {
    justifyContent: 'center',
    gap: 24,
    paddingVertical: 20,
    maxHeight: 700,
  },
  container: {
    flex: 1,
    flexDirection: 'column',
    width: '100%',
    height: '100%',
    alignItems: 'center',
    // justifyContent: 'space-between',
    backgroundColor: Dark.colors.background,
  },
  webContainer: {
    flexDirection: 'column',
    width: '100%',
    height: '100%',
    alignItems: 'center',
    backgroundColor: Dark.colors.background,
    justifyContent: 'center',
    gap: 24,
  },
  mobileHeader: {
    width: '100%',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 8,
  },
  timerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  timerContentContainer: {
    width: '100%',
    minHeight: 300,
  },
  question: {
    flex: 1,
    width: '100%',
    alignSelf: 'center',
    maxHeight: 400,
    // maxHeight: 400,
    maxWidth: 420,
  },
  footerContainer: {
    width: '100%',
    maxWidth: 420,
  },
  questionContainer: { flex: 1, justifyContent: 'center', width: '100%' },
});

export default styles;
