import React, { useCallback, useEffect, useRef, useState } from 'react';
import { KeyboardAvoidingView, Platform, View } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import _toNumber from 'lodash/toNumber';
import useGameWaitingTimer from '@/src/components/shared/game/hooks/useGameWaitingTimer';
import Header from './Header';
import Footer from '../PlayGame/Footer';
import Question from '../PlayGame/Question';
import styles from './ShowdownPlayGame.style';
import { GAME_STATUS } from '../../constants/game';
import ShowdownGameEnd from '../ShowdownGameEnd';
import ShowdownStartTimer from '../../components/ShowdownStartTimer';
import useGameQuestionsStateV2 from '../../hooks/useGameQuestionsStateV2';

const ShowdownPlayGame = ({ game }: { game: any }) => {
  const {
    startTime,
    config,
    _id: gameId,
    gameStatus,
    showdownGameConfig,
    gameType,
  } = game;
  const startTimeDate = new Date(startTime);
  const currentTime = getCurrentTime();
  const timeDiff = startTimeDate.getTime() - currentTime;

  const { timeLimit } = config;
  const endTime = startTimeDate.getTime() + _toNumber(timeLimit) * 1000;
  const hasGameEnded = endTime <= currentTime;
  const [gameEnded, setGameEnded] = useState(hasGameEnded);
  const { isMobile } = useMediaQuery();
  const showdownGamePlayers = showdownGameConfig?.showdownGamePlayer || [];
  const { currentQuestion, playersScores, submitAnswer } =
    useGameQuestionsStateV2();
  const { isReady, renderQuestionOverlay } = useGameWaitingTimer({ game });

  const onGameEnded = useCallback(() => {
    setGameEnded(true);
  }, []);

  const prevGameEndTimeoutRef = useRef<any>();

  const renderFooter = useCallback(
    () => (
      <View style={[styles.footerContainer]}>
        <Footer
          question={currentQuestion}
          submitAnswer={submitAnswer}
          gameType={gameType}
        />
      </View>
    ),
    [currentQuestion, submitAnswer, gameType],
  );

  useEffect(() => {
    const timeToGameEnd = endTime - getCurrentTime();
    if (
      timeToGameEnd <= 0 &&
      gameStatus != GAME_STATUS.ENDED &&
      !prevGameEndTimeoutRef.current
    ) {
      // endGame({gameId})
    }
    if (timeToGameEnd > 0) {
      clearTimeout(prevGameEndTimeoutRef.current);
      prevGameEndTimeoutRef.current = setTimeout(() => {
        // endGame({ gameId });
        setGameEnded(true);
      }, timeToGameEnd);
    }

    return () => {
      prevGameEndTimeoutRef.current &&
        clearTimeout(prevGameEndTimeoutRef.current);
    };
  }, [endTime, gameId, gameStatus]);

  if (gameEnded) {
    return <ShowdownGameEnd />;
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 48 : 36}
    >
      <View
        style={[styles.mainContainer, !isMobile && styles.mainContainerWeb]}
      >
        <View
          style={
            isMobile
              ? styles.mobileHeader
              : {
                  width: '100%',
                  alignItems: 'center',
                }
          }
        >
          <Header
            showdownGamePlayers={showdownGamePlayers}
            playersScores={playersScores}
            onGameEnded={onGameEnded}
            isReady={isReady}
          />
        </View>
        <View style={[styles.container, !isMobile && styles.webContainer]}>
          <View style={styles.questionContainer}>
            <View style={styles.question}>
              {timeDiff > 0 || !isReady ? (
                <ShowdownStartTimer
                  gameNumber={(showdownGameConfig?.totalGamesPlayed ?? 0) + 1}
                  time={Math.max(timeDiff, 0)}
                />
              ) : (
                <Question
                  question={currentQuestion}
                  renderQuestionOverlay={renderQuestionOverlay}
                />
              )}
            </View>
          </View>
          {!isMobile && renderFooter()}
        </View>
        {isMobile && renderFooter()}
      </View>
    </KeyboardAvoidingView>
  );
};

export default React.memo(ShowdownPlayGame);
