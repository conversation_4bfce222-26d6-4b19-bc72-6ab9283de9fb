import React from 'react';
import _isNil from 'lodash/isNil';

import Loading from 'atoms/Loading';
import ErrorView from 'atoms/ErrorView';
import { GAME_TYPES } from '../../../home/<USER>/gameTypes';

import Practice from '../Practice';
import PlayWithFriend from '../PlayWithFriend';

import { isInactiveGame } from '../../utils/gameUtils';
import ChallengeUserPlayPage from '../ChallengeUser';
import FlashAnzanDuel from '../FlashAnzan';
import ShowdownGame from '../ShowdownGame/ShowdownGame';
import InActiveGame from '../../components/InActiveGame/InActiveGame';
import FastestFingerPlay from '../FastestFinger';
import GroupPlayPage from '../GroupPlayPage';
import useGameContextV2 from '../../hooks/useGameContextV2';

const ComponentFactory = {
  [GAME_TYPES.PLAY_ONLINE]: PlayWithFriend,
  [GAME_TYPES.PLAY_WITH_FRIEND]: PlayWithFriend,
  [GAME_TYPES.PRACTICE]: Practice,
  [GAME_TYPES.ONLINE_CHALLENGE]: ChallengeUserPlayPage,
  [GAME_TYPES.FLASH_ANZAN]: FlashAnzanDuel,
  [GAME_TYPES.SUMDAY_SHOWDOWN]: ShowdownGame,
  [GAME_TYPES.FASTEST_FINGER]: FastestFingerPlay,
  [GAME_TYPES.GROUP_PLAY]: GroupPlayPage,
  [GAME_TYPES.ABILITY_DUELS]: PlayWithFriend,
};

const JoinGame = () => {
  const { game }: any = useGameContextV2();

  const { gameType } = game;

  const Component = ComponentFactory[gameType];

  if (_isNil(Component)) {
    return <ErrorView errorMessage="Invalid game type!" />;
  }

  return <Component game={game} />;
};

JoinGame.propTypes = {};

const JoinGameContainer = () => {
  const { game, gameMeta }: any = useGameContextV2();

  const { loading } = gameMeta ?? EMPTY_OBJECT;

  if (_isNil(game)) {
    if (loading) {
      return <Loading label="Loading Game" />;
    }
    return <ErrorView errorMessage="Something went wrong!" />;
  }

  if (isInactiveGame({ game })) {
    return <InActiveGame game={game} />;
  }

  return <JoinGame />;
};

export default React.memo(JoinGameContainer);
