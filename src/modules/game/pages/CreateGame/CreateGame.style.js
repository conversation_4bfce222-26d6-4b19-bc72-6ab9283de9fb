import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    overflow: 'hidden',
  },
  innerContainer: {
    flex: 1,
    paddingHorizontal: 16,
    justifyContent: 'space-between',
    paddingBottom: 15,
  },
  inputs: {
    gap: 8,
  },
  labelText: {
    fontFamily: 'Montserrat-400',
    fontSize: 12,
    color: 'white',
  },
  divider: {
    height: 1,
    width: '100%',
    backgroundColor: dark.colors.tertiary,
    marginBottom: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    // paddingHorizontal: 16,
    paddingBottom: 20,
    paddingVertical: 18,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 20,
  },
  headerTitle: {
    fontSize: 17,
    fontFamily: 'Montserrat-500',
    color: 'white',
    alignSelf: 'center',
  },
});

export default styles;
