import { gql, useMutation } from '@apollo/client';
import { useCallback } from 'react';
import _toString from 'lodash/toString';

const CANCEL_GAME_MUTATION = gql`
  mutation Mutation($gameId: ID!) {
    cancelGame(gameId: $gameId)
  }
`;

const useCancelGameById = () => {
  const [cancelGameQuery] = useMutation(CANCEL_GAME_MUTATION);

  const cancelGameById = useCallback(
    async ({ gameId }: { gameId: string }) => {
      const { data } = await cancelGameQuery({
        variables: {
          gameId: _toString(gameId),
        },
      });

      return data?.cancelGame;
    },
    [cancelGameQuery],
  );

  return {
    cancelGameById,
  };
};

export default useCancelGameById;
