import { gql, useMutation } from '@apollo/client';
import { useCallback } from 'react';
import { GAME_FRAGMENT } from '../../../../core/graphql/fragments/game';

const SUBMIT_FLASH_ANZAN_ANSWER = gql`
  ${GAME_FRAGMENT}
  mutation SubmitFlashAnzanAnswer($answerInput: SubmitFlashAnzanAnswerInput) {
    submitFlashAnzanAnswer(answerInput: $answerInput) {
      ...CoreGameFields
    }
  }
`;

const useSubmitFlashAnzanAnswer = () => {
  const [submitFlashAnzanAnswerQuery] = useMutation(SUBMIT_FLASH_ANZAN_ANSWER);

  const submitFlashAnzanAnswer = useCallback(
    (answerInput: any) => {
      return submitFlashAnzanAnswerQuery({
        variables: {
          answerInput: { ...answerInput },
        },
      });
    },
    [submitFlashAnzanAnswerQuery],
  );

  return {
    submitFlashAnzanAnswer,
  };
};

export default useSubmitFlashAnzanAnswer;
