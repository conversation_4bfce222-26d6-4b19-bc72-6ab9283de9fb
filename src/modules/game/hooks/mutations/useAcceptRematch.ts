import { gql, useMutation } from '@apollo/client';
import { useCallback } from 'react';
import _toString from 'lodash/toString';
import { GAME_FRAGMENT } from 'core/graphql/fragments/game';

const ACCEPT_REMATCH_MUTATION = gql`
  ${GAME_FRAGMENT}
  mutation AcceptRematch($gameId: ID!) {
    acceptRematch(gameId: $gameId) {
      ...CoreGameFields
    }
  }
`;

const useAcceptRematchRequest = () => {
  const [acceptRematchQuery] = useMutation(ACCEPT_REMATCH_MUTATION);

  const acceptRematchRequest = useCallback(
    ({ gameId }: { gameId: string }) => {
      return acceptRematchQuery({
        variables: {
          gameId: _toString(gameId),
        },
      });
    },
    [acceptRematchQuery],
  );

  return {
    acceptRematchRequest,
  };
};

export default useAcceptRematchRequest;
