import { gql, useQuery } from '@apollo/client';
import { GAME_FRAGMENT } from 'core/graphql/fragments/game';

const GET_GAME_DETAILED_ANALYSIS_QUERY = gql`
    query GetGameDetailedAnalysis($gameId: ID) {
        ${GAME_FRAGMENT}
        getGameDetailedAnalysis(gameId: $gameId) {
            ...CoreGameFields
        }
    }`;

const useGetGameDetailedAnalysis = ({ gameId }: { gameId: string }) => {
  const { data, loading, error } = useQuery(GET_GAME_DETAILED_ANALYSIS_QUERY, {
    fetchPolicy: 'cache-first',
    variables: {
      gameId,
    },
  });
  const { getGameDetailedAnalysis } = data ?? EMPTY_OBJECT;

  return {
    gameAnalysis: getGameDetailedAnalysis,
    loading,
    error,
  };
};

export default useGetGameDetailedAnalysis;
