import { useCallback, useMemo } from 'react';
import _isNil from 'lodash/isNil';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import _includes from 'lodash/includes';
import _isEqual from 'lodash/isEqual';
import _map from 'lodash/map';
import gameReader from 'core/readers/gameReader';

import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import _get from 'lodash/get';
import { getCurrentActiveGameId } from '@/src/core/utils/getUserCurrentActivity';
import { usePathname } from 'expo-router';
import useGoBack from '@/src/navigator/hooks/useGoBack';
import { GAME_EVENTS } from '../constants/game';
import useGameOwner from './useGameOwner';
import { useSession } from '../../auth/containers/AuthProvider';

const GAME_ID_EVENTS = {};

const useHandleGameEvents = () => {
  const { checkIsGameOwner } = useGameOwner();
  const { goBack } = useGoBack();

  const { userId: currentUserId } = useSession();

  const currentUrl = usePathname();

  const currActiveGameId = useMemo(
    () => getCurrentActiveGameId({ currentUrl }),
    [currentUrl],
  );

  const handleUserJoinEvent = useCallback(
    ({ game }) => {
      const gameId = gameReader.id(game);
      if (_includes(GAME_ID_EVENTS[gameId], GAME_EVENTS.USER_JOINED)) {
        return;
      }

      const gameType = gameReader.gameType(game);
      const timeLimit = gameReader.timeLimit(game);
      const numPlayers = gameReader.numPlayers(game);

      GAME_ID_EVENTS[gameId] = [
        ..._get(GAME_ID_EVENTS, [gameId], EMPTY_ARRAY),
        GAME_EVENTS.USER_JOINED,
      ];
      Analytics.track(ANALYTICS_EVENTS.USER_JOINED, {
        gameType,
        timeLimit,
        numPlayers,
      });
      const isGameOwner = checkIsGameOwner({ game });
      if (isGameOwner && !_isNil(game)) {
        showToast({
          type: TOAST_TYPE.SUCCESS,
          description: 'Your friend has joined the game Successfully',
        });
      }
    },
    [checkIsGameOwner],
  );

  const handleRemovePlayerEvent = useCallback(
    ({ game }) => {
      const { _id: gameId, players } = game ?? EMPTY_OBJECT;
      const isGameOwner = checkIsGameOwner({ game });
      const playersIds = _map(players, (player, index) => player.userId);

      const isCurrentUserRemoved = !_includes(playersIds, currentUserId);

      const isOnActiveGame = _isEqual(currActiveGameId, gameId);
      if (isGameOwner && !_isNil(gameId) && isOnActiveGame) {
        showToast({
          type: TOAST_TYPE.SUCCESS,
          description: 'Removed User Successfully',
        });
        return;
      }

      if (
        isOnActiveGame &&
        !isGameOwner &&
        !_isNil(gameId) &&
        isCurrentUserRemoved
      ) {
        showToast({
          type: TOAST_TYPE.INFO,
          description: 'Admin Removed you from the Game',
        });
        goBack();
      }
    },
    [checkIsGameOwner, goBack, currActiveGameId, currentUserId],
  );

  return {
    handleUserJoinEvent,
    handleRemovePlayerEvent,
  };
};

export default useHandleGameEvents;
