import React, { useContext, useEffect, useMemo, useRef } from 'react';

import _values from 'lodash/values';
import gameReader from 'core/readers/gameReader';
import useGameStore from '@/src/store/useGameStore';
import useHandleGameEvents from './useHandleGameEvents';
import useGamePlayers from './useGamePlayers';
import useGameEventSubscriptionV2 from './useGameEventSubscriptionV2';
import GameContext, { GameContextProvider } from '../context/gameContext';

const useGame = ({ gameId }: { gameId: string }) => {
  const {
    currentGame,
    updateCurrentGame,
    initialGame,
    fetchGame,
    loading,
    error,
  } = useGameStore((state) => ({
    currentGame: state.currentGame,
    updateCurrentGame: state.updateCurrentGame,
    initialGame: state.initialGame,
    fetchGame: state.fetchGame,
    loading: state.loading,
    error: state.error,
  }));

  const { submitAnswer } = useGameEventSubscriptionV2({
    gameId,
  });

  useHandleGameEvents();

  const game = useMemo(() => {
    const tempGame = currentGame ?? initialGame;
    if (gameReader.id(tempGame) !== gameId) {
      return undefined;
    }
    return tempGame;
  }, [gameId, currentGame, initialGame]);

  const { players, refreshAllPlayers } = useGamePlayers({
    game,
  });

  const updateCurrentGameRef = useRef(updateCurrentGame);
  updateCurrentGameRef.current = updateCurrentGame;

  const fetchGameRef = useRef(fetchGame);
  fetchGameRef.current = fetchGame;

  useEffect(() => {
    fetchGameRef.current(gameId);
  }, [gameId]);

  return {
    game,
    players: _values(players),
    refreshAllPlayers,
    submitAnswer,
    reFetchGame: fetchGame,
    gameMeta: {
      loading,
      error,
    },
  };
};

export const WithGameContextV2 = (Component: React.ComponentType<any>) => {
  const GameContextWrapper = (props: any) => {
    const { gameId } = props;

    const contextValue = useGame({ gameId });
    return (
      <GameContextProvider value={contextValue}>
        <Component {...props} />
      </GameContextProvider>
    );
  };

  return React.memo(GameContextWrapper);
};

const useGameContextV2 = () => useContext(GameContext);

export default useGameContextV2;
