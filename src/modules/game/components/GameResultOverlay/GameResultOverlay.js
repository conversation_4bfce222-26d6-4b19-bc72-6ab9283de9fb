import React, { useEffect, useMemo } from 'react';
import { Dimensions, Image, View } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import _size from 'lodash/size';
import _isEmpty from 'lodash/isEmpty';
import _isNil from 'lodash/isNil';
import StatikCoinsIcon from 'assets/images/game/statikCoinWhite.png';
import RatingIcon from 'assets/images/game/ratingIconWhite.png';
import victoryBg from '@/assets/images/game/victoryGradient.png';
import defeatBg from '@/assets/images/game/defeatGradient.png';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import MetricsCard from '@/src/components/shared/MetricsCard';
import { GAME_STATUS } from '../../constants/game';
import gameReader from '../../../../core/readers/gameReader';
import GameResultHeader from '../../pages/GameResultPage/components/GameResultHeader/GameResultHeader.js';
import GameResultPlayers from '../../pages/GameResultPage/components/GameResultPlayers/GameResultPlayers.js';
import GameResultFooter from '../../pages/GameResultPage/components/GameResultFooter/GameResultFooter.js';
import useGameResultOverlayStyles from './GameResultOverlay.style.js';
import useHandleGameLeaderboard from '../../hooks/useHandleGameLeaderboard';
import { WithGameContext } from '../../hooks/useGameContext';

const GAME_RESULT_SHOWN_TRACKED = {};

const GameResultOverlay = () => {
  const { isMobile } = useMediaQuery();
  const {
    game,
    players,
    isFlashAnzan,
    currentPlayer,
    player1,
    player2,
    adaptedPlayers,
    currentPlayerOriginalRating,
    currentPlayerOriginalStatikCoins,
    isCurrPlayerWinner,
    rematchWithSamePlayer,
    navigateToNewGame,
    isMatchTied,
  } = useHandleGameLeaderboard();

  const { gameType, config } = game ?? EMPTY_OBJECT;
  const gameId = gameReader.id(game);

  useEffect(() => {
    if (!gameId) return;
    if (GAME_RESULT_SHOWN_TRACKED[gameId]) return;

    GAME_RESULT_SHOWN_TRACKED[gameId] = true;
    Analytics.track(ANALYTICS_EVENTS.RESULT_PAGE_SHOWN, {
      gameType,
      ...config,
    });
  }, [config, gameId, gameType]);

  const isMinifiedQuesAvailable = useMemo(
    () =>
      !_isEmpty(game?.minifiedQuestions) && !_isNil(game?.minifiedQuestions),
    [game],
  );

  const styles = useGameResultOverlayStyles();

  if (
    _size(players) === 1 ||
    _isEmpty(game) ||
    gameReader.gameStatus(game) !== GAME_STATUS.ENDED
  ) {
    return null;
  }

  return (
    <View
      style={[
        {
          alignItems: 'center',
          justifyContent: 'center',
          flexDirection: 'row',
          backgroundColor: 'transparent',
        },
        !isMinifiedQuesAvailable && { flex: 1 },
      ]}
    >
      <View
        style={[
          styles.cardContainer,
          !isMinifiedQuesAvailable && {
            width: Dimensions.get('window').width - 32,
          },
        ]}
      >
        {!isMatchTied && (
          <Image
            source={isCurrPlayerWinner ? victoryBg : defeatBg}
            style={styles.gradientBg}
          />
        )}

        <GameResultHeader
          gameId={gameId}
          players={adaptedPlayers}
          isCurrentPlayerWinner={isCurrPlayerWinner}
          isMinifiedQuesAvailable={isMinifiedQuesAvailable}
        />
        <GameResultPlayers
          adaptedPlayers={adaptedPlayers}
          player1={player1}
          player2={player2}
          isCurrPlayerWinner={isCurrPlayerWinner}
          gameType={game?.gameType}
          isFlashAnzan={isFlashAnzan}
        />
        <View style={styles.metricsContainer}>
          <MetricsCard
            title="RATING"
            value={currentPlayerOriginalRating}
            changeValue={currentPlayer?.ratingChange}
            imageSource={RatingIcon}
            containerStyle={isMobile && { maxWidth: 200 }}
          />
          <MetricsCard
            title="TOTAL XP"
            value={currentPlayerOriginalStatikCoins}
            changeValue={currentPlayer?.statikCoinsEarned}
            imageSource={StatikCoinsIcon}
            containerStyle={isMobile && { maxWidth: 200 }}
          />
        </View>

        <GameResultFooter
          rematchWithSamePlayer={rematchWithSamePlayer}
          navigateToNewGame={navigateToNewGame}
        />
      </View>
    </View>
  );
};

export default React.memo(
  WithGameContext(GameResultOverlay, 'GameResultOverlay'),
);
