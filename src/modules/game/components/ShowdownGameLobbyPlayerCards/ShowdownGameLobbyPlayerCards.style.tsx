import { StyleSheet } from 'react-native';
import Dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    width: '100%',
    maxWidth: 420,
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  cont: {
    flex: 1,
    alignItems: 'center',
    padding: 16,
  },
  vsContainer: {
    alignItems: 'center',
    width: 80,
  },
  vsImage: {
    width: 34,
    height: 36,
    resizeMode: 'contain',
    marginBottom: 16,
  },
  gameTime: {
    fontSize: 16,
    color: Dark.colors.textDark,
    fontFamily: 'Montserrat-600',
  },
  roundInfoContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 4,
    paddingBottom: 3,
  },
  roundInfoText: {
    color: Dark.colors.textLight,
    fontSize: 10,
    lineHeight: 12.9,
    fontWeight: '700',
  },
  gamesPlayedInfo: {
    color: Dark.colors.textLight,
    fontSize: 10,
    lineHeight: 12.9,
    fontWeight: '500',
    marginTop: 4,
  },
  scoreInfo: {
    color: Dark.colors.textLight,
    fontSize: 14,
    lineHeight: 17,
    marginTop: 8,
  },
  waitingText: {
    color: Dark.colors.textLight,
    fontSize: 11,
    lineHeight: 18,
    fontWeight: '600',
    marginTop: 20,
  },
  waitTimerText: {
    color: Dark.colors.textLight,
    fontSize: 13,
    lineHeight: 20,
    fontWeight: '500',
    marginTop: 28,
    maxWidth: 260,
    textAlign: 'center',
  },
  WaitText: {
    fontSize: 12,
    lineHeight: 20,
    fontWeight: '600',
    color: Dark.colors.textDark,
    marginTop: 4,
    maxWidth: 290,
    textAlign: 'center',
  },
});

export default styles;
