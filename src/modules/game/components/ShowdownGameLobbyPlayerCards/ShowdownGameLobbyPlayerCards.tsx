import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { View } from 'react-native';
import { Text } from '@rneui/themed';
import { hideToast, showToast, TOAST_TYPE } from 'molecules/Toast';
import { router } from 'expo-router';
import useShowdownStore from 'store/useShowdownStore';
import showdownReader from 'modules/showdownV2/readers/showdownReader';
import _isNaN from 'lodash/isNaN';
import { SHOWDOWN_PLAYER_STATUS } from 'modules/showdownV2/constants/showdownPlayerStatus';
import useCountDownTimerV2 from 'core/hooks/useCountDownTimerV2';
import Card from '../usercard/UserCard';
import useGameContext from '../../hooks/useGameContext';
import { useSession } from '../../../auth/containers/AuthProvider';
import useGoBack from '../../../../navigator/hooks/useGoBack';
import styles from './ShowdownGameLobbyPlayerCards.style';

const ShowdownGameLobbyPlayerCards = () => {
  const {
    currentRound,
    numOfGames,
    joinUntilTimestamp,
    playerStatus,
    currentRoundInfo,
    refetchData,
  } = useShowdownStore((state) => ({
    currentRound: state.otherStates.currentRound,
    numOfGames: state.showdown?.roundConfig?.numOfGames,
    joinUntilTimestamp: state.otherStates.joinUntilTimestamp,
    playerStatus: showdownReader.playerStatus(state.showdown),
    currentRoundInfo: showdownReader.currentRoundInfo(state.showdown),
    refetchData: state.refetchData,
  }));

  const timeoutRef = useRef<any>(null);

  const { players, game }: any = useGameContext();
  const { showdownId } = game ?? EMPTY_OBJECT;

  const { goBack } = useGoBack();

  const currentGame = useMemo(() => {
    if (_isNaN(numOfGames) || _isNaN(currentRoundInfo?.totalGamesPlayed))
      return 1;
    return numOfGames === currentRoundInfo?.totalGamesPlayed
      ? currentRoundInfo?.totalGamesPlayed
      : (_isNaN(currentRoundInfo?.totalGamesPlayed)
          ? 0
          : currentRoundInfo.totalGamesPlayed) + 1;
  }, [numOfGames, currentRoundInfo]);

  useEffect(() => {
    if (playerStatus === SHOWDOWN_PLAYER_STATUS.OPPONENT_ABSENT) {
      showToast({
        type: TOAST_TYPE.SUCCESS,
        description:
          'Congrats! You got a free pass to the next round as your opponent didn’t show up—victory without a fight!',
      });
      router.replace(`/showdown/${showdownId}`);
    }
  }, [playerStatus, showdownId]);

  const refetchTimeoutRef = useRef<any>(null);

  const { timer, formattedTime } = useCountDownTimerV2({
    targetTimeStamp: joinUntilTimestamp,
  });
  const { user, userId } = useSession();
  const [cardDimensions, setCardDimensions] = useState({
    width: 0,
    height: 0,
  });

  const handleLayout = useCallback((event: any) => {
    const { width: parentWidth } = event.nativeEvent.layout;
    const cardWidth = Math.min(Math.max(parentWidth * 0.32, 90), 100);
    const cardHeight = cardWidth * 1.1;
    setCardDimensions({ width: cardWidth, height: cardHeight });
  }, []);

  useEffect(() => {
    if (timer > 1000) return;
    if (refetchTimeoutRef.current) clearTimeout(refetchTimeoutRef.current);
    refetchTimeoutRef.current = setTimeout(() => {
      refetchData();
    }, 2000);

    if (timeoutRef.current) clearTimeout(timeoutRef.current);

    timeoutRef.current = setTimeout(() => {
      hideToast();
      showToast({
        type: TOAST_TYPE.INFO,
        description: 'Opponent did not join',
      });
      goBack();
    }, 5000);

    return () => {
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
    };
  }, [goBack, timer, refetchData]);

  return (
    <View style={styles.cont}>
      <View style={styles.container} onLayout={handleLayout}>
        <View
          style={{
            width: cardDimensions.width,
            height: cardDimensions.height,
          }}
        >
          <Card
            user={players ? players[0] : user}
            key={players ? players[0]?.userId : userId ?? ''}
            gameType={game?.gameType}
          />
        </View>
        <View style={styles.roundInfoContainer}>
          <Text style={styles.roundInfoText}>ROUND {currentRound}</Text>
          <Text style={styles.gamesPlayedInfo}>
            Game {currentGame} of {numOfGames}
          </Text>
          <Text style={styles.scoreInfo}>0 - 0</Text>
        </View>
        <View
          style={{
            width: cardDimensions.width,
            height: cardDimensions.height,
          }}
        >
          <Card
            user={players?.[1]}
            key={players?.[1]?._id}
            gameType={game?.gameType}
          />
        </View>
      </View>
      <View style={{ alignItems: 'center' }}>
        <Text style={styles.waitingText}>
          Waiting for your opponent to join . . .
        </Text>
        <Text style={styles.waitTimerText}>
          {`You will awarded 1 point if your opponent doesn’t join in ${formattedTime}. `}
        </Text>
        <Text style={styles.WaitText}>Please don’t go back.</Text>
      </View>
    </View>
  );
};

export default React.memo(ShowdownGameLobbyPlayerCards);
