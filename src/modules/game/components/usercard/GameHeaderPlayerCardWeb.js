import React from 'react';
import { Image, StyleSheet, View } from 'react-native';
import { Text } from '@rneui/themed';

import rec1 from '@/assets/images/Rectangle5.png';
import rec2 from '@/assets/images/Rectangle6.png';

import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import Dark from '@/src/core/constants/themes/dark';
import UserImage from 'atoms/UserImage/UserImage';
import { GAME_TYPES } from '@/src/modules/home/<USER>/gameTypes';
import userReader from '../../../../core/readers/userReader';

const GameHeaderPlayerCardWeb = ({
  user = EMPTY_OBJECT,
  score = 0,
  gameType,
}) => {
  const { user: currentUser } = useSession();
  const isCurrentUser = user?._id === currentUser?._id;

  const userName = isCurrentUser ? 'You' : userReader.username(user);

  let userRating = userReader.rating(user);
  if (gameType === GAME_TYPES.FLASH_ANZAN) {
    userRating = userReader.flashAnzanRating(user);
  } else if (
    gameType === GAME_TYPES.ABILITY_DUELS ||
    gameType === GAME_TYPES.DMAS_ABILITY
  ) {
    userRating = userReader.abilityDuelsRating(user);
  }

  return (
    <View style={styles.background}>
      <Image
        source={rec1}
        style={[styles.image1, !isCurrentUser && { opacity: 0 }]}
      />
      <Image
        source={rec2}
        style={[styles.image2, !isCurrentUser && { opacity: 0 }]}
      />
      <View style={styles.card}>
        <UserImage size={34} user={user} />
        <View style={styles.userInfo}>
          <Text style={styles.userName} numberOfLines={1}>
            {userName} ({userRating})
          </Text>
          <Text style={styles.userScore}>{score}</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  background: {
    minWidth: 150,
    maxWidth: 240,
    height: 50,
    backgroundColor: Dark.colors.primary,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
    borderWidth: 1,
    borderColor: Dark.colors.tertiary,
    overflow: 'hidden',
  },
  image2: {
    position: 'absolute',
  },
  image1: {},
  card: {
    width: 150,
    height: 50,
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 8,
    padding: 8,
    marginTop: -1,
    position: 'absolute',
    gap: 8,
  },
  userInfo: {
    alignItems: 'left',
  },
  userName: {
    fontSize: 12,
    color: Dark.colors.textDark,
  },
  userScore: {
    fontSize: 16,
    marginTop: 2,
    fontFamily: 'Montserrat-700',
    color: 'white',
  },
});

export default React.memo(GameHeaderPlayerCardWeb);
