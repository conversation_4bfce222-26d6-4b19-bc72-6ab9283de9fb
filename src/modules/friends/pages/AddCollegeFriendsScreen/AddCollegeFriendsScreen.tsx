import React, { useCallback, useEffect, useRef } from 'react';
import { Text, View } from 'react-native';
import Header from '@/src/components/shared/Header';
import PaginatedList from '@/src/components/shared/PaginatedList';
import userReader from 'core/readers/userReader';
import WebBackButton from 'shared/WebBackButton';
import useMediaQuery from 'core/hooks/useMediaQuery';
import useGetUsersOfMyInstitute from '../../hooks/queries/useGetUsersOfMyInstitute';
import CollegeFriendListItem from '../../components/CollegeFriendListItem';
import styles from './AddCollegeFriendsScreen.style';

const PAGE_SIZE = 50;

const AddCollegeFriendsScreen = () => {
  const { fetchCollegeUsers, error, updateCollegeUsersCache } =
    useGetUsersOfMyInstitute({
      pageSize: PAGE_SIZE,
    });

  const paginatedListRef = useRef(null);

  const { isMobile: isCompactDevice } = useMediaQuery();

  const fetchData = useCallback(
    async ({ pageNumber }) => {
      const response = await fetchCollegeUsers({ pageNumber });
      const { data } = response ?? EMPTY_OBJECT;
      const { getUsersOfMyInstitute: usersListObj } = data ?? EMPTY_OBJECT;

      const { results, totalResults } = usersListObj ?? EMPTY_OBJECT;

      return {
        data: results,
        totalItems: totalResults,
      };
    },
    [fetchCollegeUsers],
  );

  const renderCollegeFriendItem = useCallback(
    ({ item, index, onRemove }) => (
      <CollegeFriendListItem
        userOutput={item}
        key={`${userReader.id(item?.userPublicDetails)}-${index}`}
        onRemove={onRemove}
      />
    ),
    [],
  );

  useEffect(() => {
    paginatedListRef.current?.loadData();
  }, []);

  if (error) {
    return (
      <View style={styles.container}>
        <Header title="Add College Friends" />
        <View style={styles.centeredMessageContainer}>
          <Text style={styles.errorText}>
            Error fetching college mates: {error?.message}
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View
      style={[styles.container, !isCompactDevice && { paddingHorizontal: 20 }]}
    >
      <Header title="Add College Friends" />
      {!isCompactDevice && (
        <View style={styles.headerExpanded}>
          <WebBackButton title="Add College Friends" />
        </View>
      )}
      <PaginatedList
        ref={paginatedListRef}
        key="college-friends"
        fetchData={fetchData}
        renderItem={renderCollegeFriendItem}
        keyExtractor={(item) => userReader.id(item?.userPublicDetails)}
        pageSize={PAGE_SIZE}
        pullToRefreshEnabled
        emptyListComponent={
          <View style={styles.centeredMessageContainer}>
            <Text style={styles.emptyText}>
              No college mates found, or you haven't joined an institution yet.
            </Text>
          </View>
        }
        updateCacheFunction={updateCollegeUsersCache}
        dataKey="getUsersOfMyInstitute"
      />
    </View>
  );
};

export default AddCollegeFriendsScreen;
