import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    gap: 20,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginTop: 30,
  },
  header: {
    fontSize: 12,
    fontFamily: 'Montserrat-700',
    color: dark.colors.textDark,
    letterSpacing: 0.5,
    textTransform: 'uppercase',
  },
  viewAllText: {
    fontSize: 12,
    fontFamily: 'Montserrat-700',
    color: dark.colors.secondary,
    letterSpacing: 0.5,
    textTransform: 'uppercase',
  },
});

export default styles;
