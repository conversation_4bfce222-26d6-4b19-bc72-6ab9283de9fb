import { ScrollView, Text, TouchableOpacity, View } from 'react-native';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useSession } from 'modules/auth/containers/AuthProvider';
import userReader from 'core/readers/userReader';
import { router } from 'expo-router';
import _map from 'lodash/map';
import FriendSuggestionCard from 'modules/friends/components/FriendSuggestionCard';
import styles from './FriendsRecommendationList.style';
import useGetUsersOfMyInstitute from '../../hooks/queries/useGetUsersOfMyInstitute';
import _isEmpty from 'lodash/isEmpty';
import _isNil from 'lodash/isNil';

const FriendsRecommendationList = () => {
  const { user } = useSession();

  const navigateToAddCollegeFriends = useCallback(() => {
    router.push(`/profile/${userReader.username(user)}/add-college-friends`);
  }, [user]);

  const { fetchCollegeUsers, error } = useGetUsersOfMyInstitute({
    pageSize: 10,
  });

  const fetchCollegeUsersRef = useRef(fetchCollegeUsers);
  fetchCollegeUsersRef.current = fetchCollegeUsers;

  const [isLoading, setIsLoading] = useState(false);
  const [suggestedFriends, setSuggestedFriends] = useState([]);

  useEffect(() => {
    fetchCollegeUsersRef.current({ pageNumber: 1 }).then((res) => {
      setIsLoading(false);
      const { data } = res ?? EMPTY_OBJECT;
      const { getUsersOfMyInstitute: usersListObj } = data ?? EMPTY_OBJECT;
      const { results } = usersListObj ?? EMPTY_OBJECT;
      setSuggestedFriends(results);
    });
  }, []);

  if (isLoading || !_isNil(error) || _isEmpty(suggestedFriends)) {
    return null;
  }

  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <Text style={styles.header}>Friend Suggestion</Text>
        <TouchableOpacity onPress={navigateToAddCollegeFriends}>
          <Text style={styles.viewAllText}>View All</Text>
        </TouchableOpacity>
      </View>

      <ScrollView
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ flexDirection: 'row', gap: 16 }}
      >
        <View />
        {_map(suggestedFriends, (friend) => (
          <FriendSuggestionCard key={friend?._id} userOutput={friend} />
        ))}
      </ScrollView>
    </View>
  );
};

export default React.memo(FriendsRecommendationList);
