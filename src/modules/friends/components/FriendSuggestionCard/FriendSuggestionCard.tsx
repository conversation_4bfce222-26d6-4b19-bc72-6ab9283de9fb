import React, { useCallback, useState } from 'react';
import { Text, View } from 'react-native';
import { ICON_TYPES } from '@/src/components/atoms/Icon';
import dark from '@/src/core/constants/themes/dark';
import useSendFriendRequest from 'modules/friendsAndFollowers/hooks/mutations/useSendFriendRequest';
import { useAcceptFriendRequest } from 'modules/friendsAndFollowers/hooks/mutations/useAcceptFriendRequest';
import userReader from 'core/readers/userReader';
import UserImage from 'atoms/UserImage';
import InteractiveSecondaryButton from 'atoms/InteractiveSecondaryButton';
import { FRIENDSHIP_STATUS } from '../../hooks/queries/useGetUsersOfMyInstitute';
import styles from './FriendSuggestionCard.style';

interface FriendSuggestionCardProps {
  userOutput: any;
  onSendRequest: (userId: string) => void;
  onAcceptRequest?: (userId: string) => void;
}

const getActionTextAndIcon = (currentFriendshipStatus: string) => {
  switch (currentFriendshipStatus) {
    case FRIENDSHIP_STATUS.PENDING_RECEIVED:
      return {
        text: 'ACCEPT REQ',
        icon: 'account-plus-outline',
        color: dark.colors.tertiary,
      };
    case FRIENDSHIP_STATUS.PENDING_SENT:
      return {
        text: 'SENT REQ',
        icon: 'check-circle-outline',
        color: dark.colors.tertiary,
      };
    case FRIENDSHIP_STATUS.FRIENDS:
      return {
        text: 'FRIENDS',
        icon: 'account-check-outline',
        color: dark.colors.tertiary,
      };
    default:
      return {
        text: 'SEND REQ',
        icon: 'account-plus-outline',
        color: dark.colors.tertiary,
      };
  }
};

const FriendSuggestionCard: React.FC<FriendSuggestionCardProps> = ({
  userOutput,
}) => {
  const { userPublicDetails, friendshipStatus: initialFriendshipStatus } =
    userOutput;

  const [currentFriendshipStatus, setCurrentFriendshipStatus] = useState(
    initialFriendshipStatus,
  );

  const { sendFriendRequest, isSendingFriendRequest } = useSendFriendRequest();
  const { acceptFriendRequest, loading: isAcceptingFriendRequest } =
    useAcceptFriendRequest();

  const handleActionPress = useCallback(async () => {
    if (isSendingFriendRequest || isAcceptingFriendRequest) return;
    if (currentFriendshipStatus === FRIENDSHIP_STATUS.PENDING_RECEIVED) {
      await acceptFriendRequest?.({
        senderId: userReader.id(userPublicDetails),
      });
      setCurrentFriendshipStatus(FRIENDSHIP_STATUS.FRIENDS);
    } else if (currentFriendshipStatus === FRIENDSHIP_STATUS.NOT_FRIENDS) {
      await sendFriendRequest({ receiverId: userReader.id(userPublicDetails) });
      setCurrentFriendshipStatus(FRIENDSHIP_STATUS.PENDING_SENT);
    }
  }, [
    acceptFriendRequest,
    currentFriendshipStatus,
    isAcceptingFriendRequest,
    isSendingFriendRequest,
    sendFriendRequest,
    userPublicDetails,
  ]);

  const actionDetails = getActionTextAndIcon(currentFriendshipStatus);

  return (
    <View style={styles.cardContainer}>
      <UserImage user={userPublicDetails} size={52} />
      <Text style={[styles.nameText, { marginTop: 12 }]} numberOfLines={1}>
        {userReader.displayName(userPublicDetails)}
      </Text>
      <Text
        style={[styles.usernameText, { marginBottom: 16 }]}
        numberOfLines={1}
      >
        @{userReader.username(userPublicDetails)}
      </Text>
      <InteractiveSecondaryButton
        label={actionDetails?.text}
        onPress={handleActionPress}
        buttonStyle={styles.actionButton}
        labelStyle={styles.actionButtonText}
        buttonContainerStyle={{ height: 40 }}
        iconConfig={{
          name: actionDetails?.icon,
          type: ICON_TYPES.MATERIAL_COMMUNITY_ICONS,
          size: 14,
          color: dark.colors.secondary,
        }}
        disabled={
          currentFriendshipStatus === FRIENDSHIP_STATUS.PENDING_SENT ||
          currentFriendshipStatus === FRIENDSHIP_STATUS.FRIENDS
        }
      />
    </View>
  );
};

export default FriendSuggestionCard;
