import { StyleSheet } from 'react-native';
import dark from '@/src/core/constants/themes/dark';

export default StyleSheet.create({
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 24,
    paddingHorizontal: 16,
    gap: 10,
    backgroundColor: dark.colors.background,
    borderBottomWidth: 1,
    borderBottomColor: dark.colors.tertiary,
  },
  profileImage: {
    height: 52,
    width: 52,
    borderRadius: 26,
    overflow: 'hidden',
  },
  userInfoContainer: {
    flex: 1,
    gap: 3,
    justifyContent: 'center',
  },
  nameText: {
    fontSize: 15,
    fontFamily: 'Montserrat-600',
    color: dark.colors.textLight,
    marginBottom: 2,
  },
  usernameText: {
    fontSize: 13,
    fontFamily: 'Montserrat-600',
    color: dark.colors.textDark,
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 10,
  },
  sendRequestButton: {
    backgroundColor: dark.colors.secondary,
  },
  pendingButton: {
    backgroundColor: dark.colors.tertiary,
  },
  friendsButton: {
    backgroundColor: 'transparent',
    borderColor: dark.colors.secondary,
    borderWidth: 1,
  },
});
