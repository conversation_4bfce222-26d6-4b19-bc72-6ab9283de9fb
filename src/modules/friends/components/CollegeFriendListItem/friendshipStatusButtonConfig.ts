import { FRIENDSHIP_STATUS } from 'modules/friends/hooks/queries/useGetUsersOfMyInstitute';
import { ICON_TYPES } from 'atoms/Icon';
import dark from 'core/constants/themes/dark';

export const ACTION_STATES = {
  SEND: 'SEND',
  WITHDRAW: 'WITHDRAW',
  ACCEPT: 'ACCEPT',
  UNFRIEND: 'UNFRIEND',
};

export const BUTTON_CONFIG: Record<FRIENDSHIP_STATUS, any> = {
  [FRIENDSHIP_STATUS.NOT_FRIENDS]: {
    label: 'ADD',
    iconConfig: {
      name: 'adduser',
      type: ICON_TYPES.ANT_DESIGN as any,
      size: 18,
      color: dark.colors.textLight,
    },
    action: ACTION_STATES.SEND,
  },
  [FRIENDSHIP_STATUS.PENDING_SENT]: {
    label: 'WITHDRAW',
    iconConfig: {
      name: 'user-x',
      type: ICON_TYPES.FEATHER as any,
      size: 18,
      color: dark.colors.textDark,
    },
    action: ACTION_STATES.WITHDRAW,
    confirmation: {
      title: 'Withdraw Request?',
      message: 'Do you really want to withdraw this friend request?',
      confirmText: 'WITHDRAW',
      confirmButtonType: 'primary',
    },
  },
  [FRIENDSHIP_STATUS.PENDING_RECEIVED]: {
    label: 'ACCEPT',
    iconConfig: {
      name: 'user-check',
      type: ICON_TYPES.FONT_AWESOME_5 as any,
      size: 18,
      color: dark.colors.secondary,
    },
    action: ACTION_STATES.ACCEPT,
  },
  [FRIENDSHIP_STATUS.FRIENDS]: {
    label: 'REMOVE',
    iconConfig: {
      name: 'user-minus',
      type: ICON_TYPES.FONT_AWESOME_5 as any,
      size: 18,
      color: dark.colors.errorDark,
    },
    action: ACTION_STATES.UNFRIEND,
    confirmation: {
      title: 'Remove Friend?',
      message: 'Are you sure you want to remove this friend?',
      confirmText: 'REMOVE',
      confirmButtonType: 'danger',
    },
  },
};
