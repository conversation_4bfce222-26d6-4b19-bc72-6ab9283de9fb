import { gql, useLazyQuery } from '@apollo/client';
import { useCallback } from 'react';
import _isEmpty from 'lodash/isEmpty';
import _filter from 'lodash/filter';
import _includes from 'lodash/includes';
import _size from 'lodash/size';
import userReader from 'core/readers/userReader';

export enum FRIENDSHIP_STATUS {
  FRIENDS = 'ACCEPTED',
  PENDING_SENT = 'REQUEST_SENT',
  PENDING_RECEIVED = 'PENDING_REQUEST',
  NOT_FRIENDS = 'NOT_FRIEND',
}

const DEFAULT_PAGE_SIZE = 50;

const GET_USERS_OF_MY_INSTITUTE = gql`
  query GetUsersOfMyInstitute($page: Int, $pageSize: Int) {
    getUsersOfMyInstitute(page: $page, pageSize: $pageSize) {
      results {
        userPublicDetails {
          _id
          name
          username
          bio
          country
          profileImageUrl
          rating
          ratingV2 {
            globalRating
            flashAnzanRating
            abilityDuelsRating
            puzzleRating
          }
          statikCoins
          badge
          isDeleted
          accountStatus
        }
        isFollowing
        friendshipStatus
      }
      pageNumber
      pageSize
      hasMore
      totalResults
    }
  }
`;

const useGetUsersOfMyInstitute = ({
  pageSize = DEFAULT_PAGE_SIZE,
}: { pageSize?: number } = {}) => {
  const [fetchCollegeUsersQuery, { data, loading, error, client }] =
    useLazyQuery(GET_USERS_OF_MY_INSTITUTE, {
      notifyOnNetworkStatusChange: true,
      fetchPolicy: 'network-only',
    });

  const fetchCollegeUsers = useCallback(
    async ({ pageNumber }: { pageNumber: number }) =>
      fetchCollegeUsersQuery({
        variables: {
          page: pageNumber,
          pageSize,
        },
      }),
    [fetchCollegeUsersQuery, pageSize],
  );

  const updateCollegeUsersCache = useCallback(
    async ({ addedItems = [], removedItemIds = [], pageNumber = 1 }) => {
      client.cache.updateQuery(
        {
          query: GET_USERS_OF_MY_INSTITUTE,
          variables: { page: pageNumber, pageSize },
          broadcast: true,
          overwrite: true,
        },
        (data) => {
          const { getUsersOfMyInstitute } = data ?? EMPTY_OBJECT;
          if (_isEmpty(getUsersOfMyInstitute)) {
            return data;
          }
          const { totalResults } = getUsersOfMyInstitute ?? EMPTY_OBJECT;
          let { results: updatedResults } =
            getUsersOfMyInstitute ?? EMPTY_OBJECT;

          if (!_isEmpty(addedItems)) {
            updatedResults = [...addedItems, ...updatedResults];
          }

          if (!_isEmpty(removedItemIds)) {
            updatedResults = _filter(
              updatedResults,
              (item) =>
                !_includes(
                  removedItemIds,
                  userReader.id(item?.userPublicDetails),
                ),
            );
          }

          const updatedTotalItems =
            totalResults + _size(addedItems) - _size(removedItemIds);

          return {
            ...data,
            getUsersOfMyInstitute: {
              ...getUsersOfMyInstitute,
              results: updatedResults,
              totalResults: updatedTotalItems,
            },
          };
        },
      );
    },
    [client.cache, pageSize],
  );

  return {
    loading,
    error,
    data,
    fetchCollegeUsers,
    updateCollegeUsersCache,
  };
};

export default useGetUsersOfMyInstitute;
