import { useQuery, gql } from '@apollo/client';

const GET_FEATURED_SHOWDOWN = gql`
  query GetFeaturedShowdown {
    getFeaturedShowdown {
      _id
      name
      description
      startTime
      endTime
    }
  }
`;

const useGetFeaturedShowdown = () => {
  const { data, loading, error, refetch } = useQuery(GET_FEATURED_SHOWDOWN, {
    fetchPolicy: 'cache-first',
  });
  const featuredShowdowns = data?.getFeaturedShowdown;

  return {
    featuredShowdowns: featuredShowdowns || EMPTY_ARRAY,
    loading,
    error,
    reFetchShowdowns: refetch,
  };
};

export default useGetFeaturedShowdown;
