import React, { useCallback, useMemo } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import useMediaQuery from 'core/hooks/useMediaQuery';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import dark from 'core/constants/themes/dark';
import PropTypes from 'prop-types';
import { useSession } from '../../../auth/containers/AuthProvider';
import userReader from '../../../../core/readers/userReader';
import SHOWDOWN_ACTIONS from '../../constants/showdownActions';
import styles from './ShowDownCTAButton.style';
import useShowdownCurrentStatus from '../../hooks/useShowdownCurrentStatus';

const ShowDownCTAsForUnRegisteredUsers = ({ state, onAction }) => {
  const { user } = useSession();
  const isGuest = userReader.isGuest(user);
  const { isMobile } = useMediaQuery();
  const { isRegisteringForShowDown, hasRegistrationEnded } = state;

  const {
    shouldShowRegisterButton,
    contestIsLiveAndUserNotRegistered,
    hasUserRegistered,
  } = useShowdownCurrentStatus({ state });

  const buttonStyles = useMemo(
    () => ({
      lockedRegisterButton: {
        backgroundColor: dark.colors.tertiary,
        padding: 10,
        height: isMobile ? 40 : 36,
        width: '100%',
        borderRadius: isMobile ? 50 : 8,
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'row',
        gap: 8,
      },
      registerButton: {
        backgroundColor: dark.colors.secondary,
        padding: 10,
        height: isMobile ? 40 : 36,
        width: '100%',
        borderRadius: isMobile ? 50 : 8,
        alignItems: 'center',
        justifyContent: 'center',
      },
      unRegisterButton: {
        backgroundColor: dark.colors.primary,
        paddingVertical: 10,
      },
      unregisterText: {
        fontFamily: 'Montserrat-600',
        fontSize: 13,
        lineHeight: 20,
        color: '#FF7777',
      },
      inactiveButtonText: {
        color: dark.colors.textDark,
        fontSize: isMobile ? 14 : 16,
        fontFamily: 'Montserrat-600',
      },
      inactiveButton: {
        backgroundColor: dark.colors.tertiary,
        padding: 10,
      },
      registerText: {
        color: dark.colors.card,
        fontSize: isMobile ? 14 : 16,
        fontFamily: 'Montserrat-600',
      },
    }),
    [isMobile],
  );

  const onPressRegisterByGuest = useCallback(() => {
    onAction?.({ type: SHOWDOWN_ACTIONS.REGISTER_GUEST_USER });
  }, [onAction]);

  const onPressRegister = useCallback(() => {
    onAction?.({ type: SHOWDOWN_ACTIONS.REGISTER_FOR_SHOWDOWN });
  }, [onAction]);

  const renderRegisterButtonForGuest = () => (
    <TouchableOpacity
      style={buttonStyles.lockedRegisterButton}
      onPress={onPressRegisterByGuest}
    >
      <FontAwesome name="lock" size={20} color={dark.colors.text} />
      <Text style={styles.lockedRegisterText}>Register now</Text>
    </TouchableOpacity>
  );

  const renderRegisterButton = () => (
    <TouchableOpacity
      onPress={onPressRegister}
      style={[buttonStyles.registerButton]}
      disabled={isRegisteringForShowDown}
    >
      <Text style={styles.registerText}>
        {isRegisteringForShowDown ? 'Registering...' : 'Register Now'}
      </Text>
    </TouchableOpacity>
  );

  const renderInactiveButton = ({ label, message }) => (
    <View style={[buttonStyles.registerButton, buttonStyles.inactiveButton]}>
      <Text style={buttonStyles.inactiveButtonText}>{label}</Text>
    </View>
  );

  if (hasRegistrationEnded) {
    return renderInactiveButton({
      label: 'Registration Ended',
      message: 'Registration has ended, You cant participate in this Showdown',
    });
  }
  if (hasUserRegistered) {
    return null;
  }

  if (shouldShowRegisterButton) {
    if (isGuest) {
      return renderRegisterButtonForGuest();
    }
    return renderRegisterButton();
  }
  return (
    <View style={buttonStyles.lockedRegisterButton}>
      <Text style={styles.lockedRegisterText}>
        {contestIsLiveAndUserNotRegistered
          ? 'Showdown started'
          : 'Showdown Ended'}
      </Text>
    </View>
  );
};

ShowDownCTAsForUnRegisteredUsers.propTypes = {
  state: PropTypes.object.isRequired,
  onAction: PropTypes.func.isRequired,
};

export default React.memo(ShowDownCTAsForUnRegisteredUsers);
