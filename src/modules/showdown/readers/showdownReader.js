import _property from 'lodash/property'
import _get from 'lodash/get'

const showdownReader = {
  id: _property('_id'),
  name: _property('name'),
  users: _property('users'),
  roundsCount: _property('rounds'),
  startTime: _property('startTime'),
  gapBwRounds: _property('gapBwRounds'),
  roundTime: _property('roundTime'),
  endTime: _property('endTime'),
  registrationCount: _property('registrationCount'),
  currentRoundNumber: _property('currentRound'),
  registrationEndTime: _property('registrationEndTime'),
  currentUserParticipation: _property('currentUserParticipation'),
  currentRoundInfo: _property(['currentUserParticipation', 'currentRound']),
  playerStatus: (showdown) =>
    _get(showdownReader.currentRoundInfo(showdown), 'playerStatus'),
}

export default showdownReader
