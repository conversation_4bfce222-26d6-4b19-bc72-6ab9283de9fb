import React, { useCallback } from 'react';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import matiks from 'assets/images/LinearGradientIcons/matiksBolt.png';
import dark from '@/src/core/constants/themes/dark';
import { useRouter } from 'expo-router';
import { BLOGS_ROUTE } from '@/src/core/constants/appConstants';

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    gap: 8,
    justifyContent: 'space-between',
    marginRight: 44,
  },
  image: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
  },
  text: {
    color: dark.colors.textDark,
    fontSize: 12,
    fontFamily: 'Montserrat-500',
  },
  routesContainer: { flexDirection: 'row', gap: 28, marginRight: 32 },
});

const Header = () => {
  const router = useRouter();
  const onPressPrivacyPolicy = useCallback(() => {
    router.push('/terms-and-conditions');
  }, []);
  const onPressBlogs = useCallback(() => {
    router.push(BLOGS_ROUTE);
  }, []);
  return (
    <View style={styles.container}>
      <Image source={matiks} style={styles.image} />
      <View style={styles.routesContainer}>
        <TouchableOpacity onPress={onPressBlogs}>
          <Text style={styles.text}>Blogs</Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={onPressPrivacyPolicy}>
          <Text style={styles.text}>Privacy Policy</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default React.memo(Header);
