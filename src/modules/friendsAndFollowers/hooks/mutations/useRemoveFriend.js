import { gql, useMutation } from '@apollo/client';
import { useCallback } from 'react';

const REMOVE_FRIEND = gql`
  mutation RemoveFriend($removeFriendInput: RemoveFriendInput!) {
    removeFriend(removeFriendInput: $removeFriendInput)
  }
`;

export const useRemoveFriend = () => {
  const [removeFriendQuery, { loading }] = useMutation(REMOVE_FRIEND);

  const removeFriend = useCallback(
    async ({ receiverId }) => {
      const response = await removeFriendQuery({
        variables: {
          removeFriendInput: {
            userId: receiverId,
          },
        },
      });

      return response;
    },
    [removeFriendQuery],
  );

  return {
    removeFriend,
    loading,
  };
};
