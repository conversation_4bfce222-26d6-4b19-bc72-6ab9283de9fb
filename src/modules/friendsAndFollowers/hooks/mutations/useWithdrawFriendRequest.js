import { gql, useMutation } from '@apollo/client';
import { useCallback } from 'react';

const WITHDRAW_FRIEND_REQUEST = gql`
  mutation WithdrawFriendRequest(
    $withdrawFriendRequestInput: WithdrawFriendRequestInput!
  ) {
    withdrawFriendRequest(
      withdrawFriendRequestInput: $withdrawFriendRequestInput
    )
  }
`;

const useWithdrawFriendRequest = () => {
  const [withdrawFriendRequestQuery, { loading }] = useMutation(
    WITHDRAW_FRIEND_REQUEST,
  );

  const withdrawFriendRequest = useCallback(
    async ({ receiverId }) => {
      const response = await withdrawFriendRequestQuery({
        variables: {
          withdrawFriendRequestInput: {
            userId: receiverId,
          },
        },
      });

      return response;
    },
    [withdrawFriendRequestQuery],
  );

  return {
    withdrawFriendRequest,
    loading,
  };
};

export default useWithdrawFriendRequest;
