import { gql, useMutation } from '@apollo/client';
import { useCallback } from 'react';

const ACCEPT_FRIEND_REQUEST = gql`
  mutation AcceptFriendRequest($acceptRequestInput: FriendRequestInput!) {
    acceptFriendRequest(acceptRequestInput: $acceptRequestInput)
  }
`;

export const useAcceptFriendRequest = () => {
  const [accpetFriendRequestQuery, { loading }] = useMutation(
    ACCEPT_FRIEND_REQUEST,
  );

  const acceptFriendRequest = useCallback(
    async ({ senderId }) => {
      const response = await accpetFriendRequestQuery({
        variables: {
          acceptRequestInput: {
            userId: senderId,
          },
        },
      });

      return response;
    },
    [accpetFriendRequestQuery],
  );

  return {
    acceptFriendRequest,
    loading,
  };
};
