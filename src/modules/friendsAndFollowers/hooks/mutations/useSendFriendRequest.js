import { gql, useMutation } from '@apollo/client';
import { useCallback } from 'react';

const SEND_FRIEND_REQUEST = gql`
  mutation SendFriendRequest($sendRequestInput: FriendRequestInput) {
    sendFriendRequest(sendRequestInput: $sendRequestInput)
  }
`;

const useSendFriendRequest = () => {
  const [sendFriendRequestQuery, { loading }] =
    useMutation(SEND_FRIEND_REQUEST);

  const sendFriendRequest = useCallback(
    async ({ receiverId }) => {
      const response = await sendFriendRequestQuery({
        variables: {
          sendRequestInput: {
            userId: receiverId,
          },
        },
      });

      return response;
    },
    [sendFriendRequestQuery],
  );

  return {
    sendFriendRequest,
    isSendingFriendRequest: loading,
  };
};

export default useSendFriendRequest;
