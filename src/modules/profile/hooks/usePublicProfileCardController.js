import { useCallback, useEffect, useState } from 'react';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { useRouter } from 'expo-router';
import _isEqual from 'lodash/isEqual';
import _toString from 'lodash/toString';
import { showRightPane } from 'molecules/RightPane/RightPane';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import userReader from 'core/readers/userReader';
import useFollowUser from '../../friendsAndFollowers/hooks/mutations/useFollowUser';
import useUnFollowUser from '../../friendsAndFollowers/hooks/mutations/useUnfollowUser';
import useSendFriendRequest from '../../friendsAndFollowers/hooks/mutations/useSendFriendRequest';
import { useRemoveFriend } from '../../friendsAndFollowers/hooks/mutations/useRemoveFriend';
import { useSession } from '../../auth/containers/AuthProvider';
import FollowersPage from '../../friendsAndFollowers/pages/FollowersPage';
import FollowingsPage from '../../friendsAndFollowers/pages/FollowingsPage';
import { FRIENDSHIP_STATUS } from '../../friendsAndFollowers/constants/friendshipStatus';
import { useAcceptFriendRequest } from '../../friendsAndFollowers/hooks/mutations/useAcceptFriendRequest';
import useWithdrawFriendRequest from '../../friendsAndFollowers/hooks/mutations/useWithdrawFriendRequest';
import useGetUserByUserNameQuery from '../../../core/hooks/useGetUserByUsername';
import Analytics from '../../../core/analytics';
import { ANALYTICS_EVENTS } from '../../../core/analytics/const';
import useChallengeUser from '../../friendsAndFollowers/hooks/mutations/useChallengeUser';

const usePublicProfileCardController = ({
  user,
  isFollowing: isFollowingInitialValue,
  friendshipStatus: friendshipStatusInitialValue,
}) => {
  const { _id: searchedUserId, username } = user ?? EMPTY_OBJECT;
  const { updateSearchedUserCache } = useGetUserByUserNameQuery({
    userName: username,
  });
  const { isMobile: isCompactMode } = useMediaQuery();
  const router = useRouter();
  const { followUser } = useFollowUser();
  const { unFollowUser } = useUnFollowUser();
  const { sendFriendRequest } = useSendFriendRequest();
  const { removeFriend } = useRemoveFriend();
  const { acceptFriendRequest } = useAcceptFriendRequest();
  const { withdrawFriendRequest } = useWithdrawFriendRequest();
  const { challengeUser } = useChallengeUser();
  const { user: currentUser, updateCurrentUser } = useSession();
  const { isGuest: isCurrentUserIsGuest } = currentUser ?? {};
  const { isGuest } = user ?? {};

  const [isFollowing, setIsFollowing] = useState(isFollowingInitialValue);
  const [friendshipStatus, setFriendshipStatus] = useState(
    friendshipStatusInitialValue,
  );
  const [isFollowFuncInQueue, setIsFollowFuncInQueue] = useState(false);
  const [isUnFollowFuncInQueue, setIsUnFollowFuncInQueue] = useState(false);
  const [isSendingFriendReq, setIsSendingFriendReq] = useState(false);
  const [isRemovingFriend, setIsRemovingFriend] = useState(false);
  const [isWithdrawingFriendReq, setIsWithdrawingFriendReq] = useState(false);
  const [isAcceptingFriendReq, setIsAcceptingFriendReq] = useState(false);
  const [isChallengingFriend, setIsChallengingFriend] = useState(false);

  const updateCurrentUserStats = useCallback(
    async ({ keyName, increment }) => {
      const { stats } = currentUser ?? EMPTY_OBJECT;
      const updatedCount = increment
        ? (stats[keyName] ?? 0) + 1
        : (stats[keyName] ?? 0) - 1;
      updateCurrentUser({ stats: { ...stats, [keyName]: updatedCount } });
    },
    [updateCurrentUser, currentUser],
  );

  const navigateToFindFriendsPage = useCallback(() => {
    router.push(`/profile/${userReader.username(currentUser)}/find-friends`);
  }, [currentUser, router]);

  useEffect(() => {
    if (!_isEqual(friendshipStatus, friendshipStatusInitialValue)) {
      setFriendshipStatus(friendshipStatusInitialValue);
    }
    if (isFollowingInitialValue !== isFollowing) {
      setIsFollowing(isFollowingInitialValue);
    }
  }, [friendshipStatusInitialValue, isFollowingInitialValue]);

  const updateSearchedUserStats = useCallback(
    (updatedStats, additionalDetails) => {
      const { stats } = user ?? EMPTY_OBJECT;
      updateSearchedUserCache(
        {
          stats: {
            ...stats,
            ...updatedStats,
          },
        },
        additionalDetails,
      );
    },
    [user, updateSearchedUserCache],
  );

  const handleOnFollowOrUnfollowPressed = useCallback(async () => {
    if (isFollowFuncInQueue || isUnFollowFuncInQueue) return;
    try {
      if (!isFollowing) {
        setIsFollowFuncInQueue(true);
        Analytics.track(
          ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS
            .CLICKED_ON_FOLLOW_USER_FROM_PROFILE_VIEW,
        );
        const { data } =
          (await followUser({ followeeId: searchedUserId })) ?? EMPTY_OBJECT;
        if (data?.followUser === true) {
          setIsFollowing(true);
          updateSearchedUserStats(
            { followersCount: (user?.stats?.followersCount || 0) + 1 },
            { isFollowing: true },
          );
          updateCurrentUserStats({
            keyName: 'followingsCount',
            increment: true,
          });
          showToast({
            type: TOAST_TYPE.SUCCESS,
            timeInterval: 3200,
            description: `You're now following ${username}`,
          });
        }
      } else {
        setIsUnFollowFuncInQueue(true);
        Analytics.track(
          ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS
            .CLICKED_ON_UNFOLLOW_USER_FROM_PROFILE_VIEW,
        );
        const { data } =
          (await unFollowUser({ followeeId: searchedUserId })) ?? EMPTY_OBJECT;
        if (data?.unFollowUser === true) {
          setIsFollowing(false);
          updateSearchedUserStats(
            { followersCount: (user?.stats?.followersCount || 0) - 1 },
            { isFollowing: false },
          );
          updateCurrentUserStats({
            keyName: 'followingsCount',
            increment: false,
          });
          showToast({
            type: TOAST_TYPE.SUCCESS,
            timeInterval: 3200,
            description: `Unfollowed ${username} successfully`,
          });
        }
      }
    } catch (error) {
      console.error(error);
    } finally {
      setIsFollowFuncInQueue(false);
      setIsUnFollowFuncInQueue(false);
    }
  }, [
    followUser,
    unFollowUser,
    user,
    isFollowing,
    isFollowFuncInQueue,
    isUnFollowFuncInQueue,
    username,
  ]);

  const handleOnAddOrRemoveFriendPressed = useCallback(async () => {
    if (
      isSendingFriendReq ||
      isRemovingFriend ||
      isAcceptingFriendReq ||
      isWithdrawingFriendReq
    )
      return;
    try {
      if (friendshipStatus === FRIENDSHIP_STATUS.NOT_FRIEND) {
        setIsSendingFriendReq(true);
        Analytics.track(
          ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS
            .CLICKED_ON_SEND_FRIEND_REQUEST_FROM_PROFILE_VIEW,
        );
        const { data } =
          (await sendFriendRequest({ receiverId: searchedUserId })) ??
          EMPTY_OBJECT;
        if (data?.sendFriendRequest === true) {
          setFriendshipStatus(FRIENDSHIP_STATUS.REQUEST_SENT);
          updateSearchedUserStats(
            {
              followersCount: !isFollowing
                ? (user?.stats?.followersCount || 0) + 1
                : user?.stats?.followersCount,
            },
            {
              friendshipStatus: FRIENDSHIP_STATUS.REQUEST_SENT,
              isFollowing: true,
            },
          );
          showToast({
            type: TOAST_TYPE.SUCCESS,
            timeInterval: 3200,
            description: `Friend Request to ${username} Sent Successfully`,
          });
        }
      } else if (friendshipStatus === FRIENDSHIP_STATUS.ACCEPTED) {
        setIsRemovingFriend(true);
        Analytics.track(
          ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS
            .CLICKED_ON_REMOVE_FRIEND_FROM_PROFILE_VIEW,
        );
        const { data } = await removeFriend({ receiverId: searchedUserId });
        if (data?.removeFriend === true) {
          setFriendshipStatus(FRIENDSHIP_STATUS.NOT_FRIEND);
          updateSearchedUserStats(
            { friendsCount: (user?.stats?.friendsCount || 1) - 1 },
            { friendshipStatus: FRIENDSHIP_STATUS.NOT_FRIEND },
          );
          updateCurrentUserStats({ keyName: 'friendsCount', increment: false });
          showToast({
            type: TOAST_TYPE.SUCCESS,
            timeInterval: 3200,
            description: `${username} removed from your friends list`,
          });
        }
      } else if (friendshipStatus === FRIENDSHIP_STATUS.REQUEST_SENT) {
        setIsWithdrawingFriendReq(true);
        Analytics.track(
          ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS
            .CLICKED_ON_WITHDRAW_FRIEND_REQUEST_FROM_PROFILE_VIEW,
        );
        const { data } = await withdrawFriendRequest({
          receiverId: searchedUserId,
        });
        if (data?.withdrawFriendRequest === true) {
          setFriendshipStatus(FRIENDSHIP_STATUS.NOT_FRIEND);
          updateSearchedUserStats(
            {},
            { friendshipStatus: FRIENDSHIP_STATUS.NOT_FRIEND },
          );
          showToast({
            type: TOAST_TYPE.SUCCESS,
            description: `Friend Request to ${username} Withdrawn Successfully`,
            timeInterval: 3200,
          });
        }
      } else if (friendshipStatus === FRIENDSHIP_STATUS.PENDING_REQUEST) {
        setIsAcceptingFriendReq(true);
        Analytics.track(
          ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS
            .CLICKED_ON_ACCEPT_FRIEND_REQUEST_FROM_PROFILE_VIEW,
        );
        const { data } = await acceptFriendRequest({
          senderId: searchedUserId,
        });
        if (data?.acceptFriendRequest === true) {
          setFriendshipStatus(FRIENDSHIP_STATUS.ACCEPTED);
          updateSearchedUserStats(
            { friendsCount: (user?.stats?.friendsCount || 0) + 1 },
            { friendshipStatus: FRIENDSHIP_STATUS.ACCEPTED },
          );
          updateCurrentUserStats({ keyName: 'friendsCount', increment: true });
          showToast({
            type: TOAST_TYPE.SUCCESS,
            description: `You and ${username} are now friends`,
            timeInterval: 3200,
          });
        }
      }
    } catch (error) {
      console.error(error);
    } finally {
      setIsSendingFriendReq(false);
      setIsRemovingFriend(false);
      setIsWithdrawingFriendReq(false);
      setIsAcceptingFriendReq(false);
    }
  }, [
    friendshipStatus,
    sendFriendRequest,
    removeFriend,
    withdrawFriendRequest,
    acceptFriendRequest,
    user,
    isSendingFriendReq,
    isRemovingFriend,
    isAcceptingFriendReq,
    isWithdrawingFriendReq,
    username,
  ]);

  const navigateToFollowersPage = useCallback(() => {
    if (!_isEqual(_toString(currentUser?._id), _toString(searchedUserId)))
      return;
    Analytics.track(
      ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS.CLICKED_ON_FOLLOWERS_COUNT_TEXT,
    );
    if (isCompactMode) {
      router.push(`/profile/${user?.username}/followers`);
      return;
    }
    showRightPane({ content: <FollowersPage /> });
  }, [currentUser?._id, searchedUserId, isCompactMode, router, user?.username]);

  const navigateToFollowingsPage = useCallback(() => {
    if (!_isEqual(_toString(currentUser?._id), _toString(searchedUserId)))
      return;
    Analytics.track(
      ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS.CLICKED_ON_FOLLOWINGS_COUNT_TEXT,
    );
    if (isCompactMode) {
      router.push(`/profile/${user?.username}/followings`);
      return;
    }
    showRightPane({ content: <FollowingsPage /> });
  }, [currentUser?._id, searchedUserId, isCompactMode, router, user?.username]);

  const navigateToFriendsPage = useCallback(() => {
    if (!_isEqual(_toString(currentUser?._id), _toString(searchedUserId)))
      return;
    Analytics.track(
      ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS.CLICKED_ON_FRIENDS_COUNT_TEXT,
    );
    router.push(`/profile/${user?.username}/friends`);
  }, [currentUser?._id, router, searchedUserId, user?.username]);

  const handleOnChallengePressed = useCallback(async () => {
    if (isChallengingFriend) {
      return;
    }
    try {
      Analytics.track(
        ANALYTICS_EVENTS.FRIENDS_AND_FOLLOWERS
          .CLICKED_ON_CHALLENGE_FRIEND_FROM_PROFILE_VIEW,
      );
      setIsChallengingFriend(true);
      // await challengeUser({ userId: searchedUserId });
      router.push(`/games/lobby?friendId=${searchedUserId}`);
    } catch (e) {
      setIsChallengingFriend(false);
    } finally {
      setIsChallengingFriend(false);
    }
  }, [setIsChallengingFriend, searchedUserId, isChallengingFriend]);

  return {
    isFollowing,
    friendshipStatus,
    isFollowFuncInQueue,
    isUnFollowFuncInQueue,
    isSendingFriendReq,
    isRemovingFriend,
    isCurrentUserIsGuest,
    isGuest,
    handleOnAddOrRemoveFriendPressed,
    handleOnFollowOrUnfollowPressed,
    navigateToFollowingsPage,
    navigateToFollowersPage,
    navigateToFriendsPage,
    handleOnChallengePressed,
    navigateToFindFriendsPage,
  };
};

export default usePublicProfileCardController;
