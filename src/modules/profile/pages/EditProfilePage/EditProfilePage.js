import React, { useCallback, useState } from 'react';
import {
  ActivityIndicator,
  Image,
  ScrollView,
  TouchableOpacity,
  View,
} from 'react-native';
import { Text } from '@rneui/themed';
import Entypo from '@expo/vector-icons/Entypo';
import EvilIcons from '@expo/vector-icons/EvilIcons';
import UserImage from 'atoms/UserImage';
import PrimaryButton from '@/src/components/atoms/PrimaryButton';
import { closeRightPane } from 'molecules/RightPane/RightPane';
import AchievementIcon from '@/assets/images/icons/achievement.png';
import _isEmpty from 'lodash/isEmpty';
import _map from 'lodash/map';
import dark from 'core/constants/themes/dark';
import _size from 'lodash/size';
import _toLower from 'lodash/toLower';
import { withOpacity } from 'core/utils/colorUtils';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import Icon, { ICON_TYPES } from 'atoms/Icon';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import useMediaQuery from 'core/hooks/useMediaQuery';
import DropdownComponent from 'atoms/Dropdown';
import COUNTRIES_LIST from 'core/constants/countries';
import userReader from 'core/readers/userReader';
import useEditProfileController from '../../hooks/useEditProfileController';
import TextInputWithLabel from './components/TextInputWithLabel/TextInputWithLabel';
import SaveChangesPopover from './components/SaveChangesPopover';
import styles from './EditProfile.style';

const EditProfileScreen = (props) => {
  const { isMobile: isCompactMode } = useMediaQuery();
  const [isOverlayVisible, setOverlayVisible] = useState(false);
  const { updateCurrentUser } = useSession();
  const {
    user,
    editedUserDetails,
    errors,
    isImagePopoverVisible,
    isSavePopoverVisible,
    selectedImage,
    isImageLoading,
    compressionStatus,
    achievements,
    socialHandles,
    hasUserChangedFields,
    isUpdatingDetails,
    handleSave,
    handleInputChange,
    handleRemoveImage,
    handleChooseImage,
    isFormValid,
    toggleImagePopover,
    toggleSavePopover,
    handleAchievementChange,
    handleAddAchievement,
    handleRemoveAchievement,
    handleAddSocialHandle,
    handleRemoveSocialHandle,
    handleSocialHandleChange,
    pickAchievementImage,
    isUsernameAvailable,
    isCheckingUsername,
    editedUserName,
    goBack,
    updateUser,
  } = useEditProfileController();

  const handleRemoveCollege = useCallback(() => {
    try {
      updateCurrentUser?.({ institutionId: null, institutionName: null });
      updateUser?.({ removeInstitution: true }).then(() => {
        showToast({
          type: TOAST_TYPE.SUCCESS,
          description: 'College removed successfully!',
        });
      });
    } catch (error) {
      console.error('Failed to remove college:', error);
    }
  }, [updateCurrentUser, updateUser]);

  const renderUserNameRightAccessories = useCallback(() => {
    if (isCheckingUsername) {
      return (
        <ActivityIndicator
          size="small"
          color={dark.colors.success}
          style={{ height: 24, width: 24, flexShrink: 1 }}
        />
      );
    }
    if (!isUsernameAvailable) {
      return (
        <Entypo
          name="circle-with-cross"
          size={18}
          color={dark.colors.errorDark}
        />
      );
    }
    if (isUsernameAvailable && _size(editedUserName) > 0) {
      return <Entypo name="check" size={18} color={dark.colors.secondary} />;
    }
  }, [isCheckingUsername, isUsernameAvailable]);

  const formValid = isFormValid();

  return (
    <View style={styles.container}>
      <View style={{ gap: 5, flex: 1, width: '100%' }}>
        {isCompactMode && (
          <View style={styles.headerCompact}>
            <Entypo name="cross" size={24} color="white" onPress={goBack} />
            <Text style={styles.editTextStyle}>Edit Profile</Text>
          </View>
        )}

        {!isCompactMode && (
          <View style={styles.headerExpanded}>
            <Text style={styles.editTextStyle}>Edit Profile</Text>
            <Entypo
              name="cross"
              size={24}
              color="white"
              onPress={closeRightPane}
            />
          </View>
        )}

        <ScrollView
          contentContainerStyle={styles.inputFields}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.imageContainer}>
            <TouchableOpacity onPress={handleChooseImage}>
              {isImageLoading ? (
                <View style={[styles.userImage, styles.loadingContainer]}>
                  <ActivityIndicator size="small" color="#ffffff" />
                  {!_isEmpty(compressionStatus) && (
                    <Text style={styles.compressionStatus}>
                      {compressionStatus}
                    </Text>
                  )}
                </View>
              ) : selectedImage ? (
                <Image
                  source={{ uri: selectedImage }}
                  style={styles.userImage}
                />
              ) : (
                <UserImage
                  user={user}
                  rounded={false}
                  style={styles.userImage}
                />
              )}
              <View style={styles.editImageOverlay}>
                <EvilIcons name="pencil" size={20} color="black" />
              </View>
            </TouchableOpacity>
          </View>
          <Text style={[styles.sectionTitle, { marginBottom: 0 }]}>
            USER INTRO
          </Text>
          <TextInputWithLabel
            key="username"
            label="User Name"
            placeholderText="Enter User Name"
            value={editedUserDetails.username}
            renderRightAccessories={renderUserNameRightAccessories}
            error={errors.username}
            onChangeText={(text) =>
              handleInputChange({ key: 'username', text: _toLower(text) })
            }
          />

          <TextInputWithLabel
            key="full-name"
            label="Full Name"
            placeholderText="Enter Full Name"
            value={editedUserDetails.name}
            error={errors.name}
            onChangeText={(text) => handleInputChange({ key: 'name', text })}
          />

          <View style={styles.inputContainer}>
            <View style={{ flexDirection: 'row', gap: 3 }}>
              <Text style={styles.inputLabel}>Country</Text>
              <Text style={styles.requiredTag}>*</Text>
            </View>
            <DropdownComponent
              key="country-dropdown"
              data={COUNTRIES_LIST}
              value={editedUserDetails.countryCode}
              onChange={(text) =>
                handleInputChange({ key: 'countryCode', text })
              }
              placeholderText="Select Country"
              searchPlaceholderText="Search Country"
              error={errors.countryCode}
            />
            {!_isEmpty(errors.countryCode) && (
              <Text style={styles.errorText}>{errors.countryCode}</Text>
            )}
          </View>

          <TextInputWithLabel
            key="user-bio"
            label="Your Bio"
            isRequired={false}
            placeholderText="Enter Your Bio (Optional)"
            value={editedUserDetails.bio}
            error={errors.bio}
            onChangeText={(text) => handleInputChange({ key: 'bio', text })}
            multiline
            numberOfLines={4}
          />

          {userReader.institutionId(user) && (
            <View style={styles.inputContainer}>
              <View style={{ flexDirection: 'row', gap: 3 }}>
                <Text style={styles.inputLabel}>College/Institute</Text>
                <Text style={styles.requiredTag}>*</Text>
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}
              >
                <Text
                  style={[
                    styles.inputLabel,
                    {
                      marginBottom: 0,
                      fontSize: 13,
                      fontFamily: 'Montserrat-500',
                    },
                  ]}
                >
                  {userReader.institutionName(user)}
                </Text>

                <TouchableOpacity onPress={handleRemoveCollege}>
                  <Icon
                    color={dark.colors.defeatColor}
                    name="delete"
                    size={18}
                    type={ICON_TYPES.MATERIAL_ICONS}
                    style={{ marginLeft: 8 }}
                  />
                </TouchableOpacity>
              </View>
            </View>
          )}

          <View
            key="Awards-And-Achievements"
            style={{ gap: 10, marginTop: 10 }}
          >
            <Text style={styles.sectionTitle}>AWARDS & ACHIEVEMENTS</Text>
            {_map(achievements, (achievement, index) => (
              <View
                key={`achievement-${index}`}
                style={styles.achievementContainer}
              >
                <View
                  style={{
                    flexDirection: 'row',
                    gap: 10,
                    alignItems: 'flex-start',
                  }}
                >
                  <TouchableOpacity
                    onPress={() => pickAchievementImage({ index })}
                    style={styles.achievementImageContainer}
                  >
                    <Image
                      source={achievement?.imageUrl ?? AchievementIcon}
                      resizeMode="cover"
                      style={styles.achievementImage}
                    />
                    <View style={[styles.editImageOverlay, { left: 20 }]}>
                      <EvilIcons name="pencil" size={15} color="black" />
                    </View>
                  </TouchableOpacity>

                  <View style={{ flex: 1 }}>
                    <TextInputWithLabel
                      key={`achievement-title-${index}`}
                      label="Title"
                      placeholderText="Achievement Title"
                      value={achievement.title}
                      onChangeText={(text) =>
                        handleAchievementChange(index, 'title', text)
                      }
                    />
                  </View>
                </View>
                <TextInputWithLabel
                  key={`achievement-description-${index}`}
                  label="Description "
                  placeholderText="Add Description (optional)"
                  value={achievement.description}
                  isRequired={false}
                  onChangeText={(text) =>
                    handleAchievementChange(index, 'description', text)
                  }
                />
                <TextInputWithLabel
                  key={`achievement-link-${index}`}
                  label="Reference Link"
                  placeholderText="Add Reference Link(optional)"
                  value={achievement.link}
                  isRequired={false}
                  error={achievement.error}
                  onChangeText={(text) =>
                    handleAchievementChange(index, 'link', text)
                  }
                />

                <TouchableOpacity
                  onPress={() => handleRemoveAchievement(index)}
                  style={styles.removeButton}
                >
                  <Text style={styles.removeButtonText}>Remove</Text>
                </TouchableOpacity>
              </View>
            ))}
            <View style={styles.addButtonRow}>
              <PrimaryButton
                label="Add  +"
                onPress={handleAddAchievement}
                labelStyle={styles.addButtonText}
                buttonStyle={styles.addButton}
              />
            </View>
          </View>

          <View key="Social Links" style={{ gap: 10, marginTop: 10 }}>
            <Text style={styles.sectionTitle}>SOCIAL PROFILES LINK</Text>
            {_map(socialHandles, (handle, index) => (
              <View key={`social-handle-${index}`}>
                <TextInputWithLabel
                  key={`social-link-${index}`}
                  label={`Link - ${index + 1}`}
                  placeholderText="Profile Link"
                  value={handle.link}
                  onChangeText={(text) =>
                    handleSocialHandleChange(index, 'link', text)
                  }
                  error={handle.error}
                />

                <TouchableOpacity
                  onPress={() => handleRemoveSocialHandle(index)}
                  style={styles.removeButton}
                >
                  <Text style={styles.removeButtonText}>Remove</Text>
                </TouchableOpacity>
              </View>
            ))}
            <View style={styles.addButtonRow}>
              <PrimaryButton
                label="Add  +"
                labelStyle={styles.addButtonText}
                onPress={handleAddSocialHandle}
                buttonStyle={styles.addButton}
              />
            </View>
          </View>
        </ScrollView>
        <PrimaryButton
          label="Save"
          onPress={toggleSavePopover}
          buttonStyle={[
            styles.saveButton,
            !isCompactMode && {
              marginHorizontal: 0,
            },
          ]}
          labelStyle={[
            !formValid && { color: withOpacity(dark.colors.tertiary, 0.4) },
          ]}
          disabled={!formValid || !hasUserChangedFields}
        />
      </View>
      {isSavePopoverVisible && (
        <SaveChangesPopover
          isUpdatingDetails={isUpdatingDetails}
          isVisible={isSavePopoverVisible}
          toggleSavePopover={toggleSavePopover}
          handleSave={handleSave}
        />
      )}

      {/* {isImagePopoverVisible && ( */}
      {/*    <SelectImagePopover */}
      {/*        isVisible={isImagePopoverVisible} */}
      {/*        onBackdropPress={toggleImagePopover} */}
      {/*        toggleImagePopover={toggleImagePopover} */}
      {/*        handleChooseImage={handleChooseImage} */}
      {/*        handleRemoveImage={handleRemoveImage} /> */}
      {/* )} */}
    </View>
  );
};

export default React.memo(EditProfileScreen);
