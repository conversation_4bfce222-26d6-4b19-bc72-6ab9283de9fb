import React, { useCallback } from 'react';
import { StyleSheet, View } from 'react-native';

import dark from 'core/constants/themes/dark';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import useMediaQuery from 'core/hooks/useMediaQuery';
import useGoBack from 'navigator/hooks/useGoBack';
import { useSession } from 'modules/auth/containers/AuthProvider';
import useUpdateUserProfile from '../../hooks/query/useUpdateUserProfile';
import SelectInstitutionComponent from '../../components/SelectInstitutionComponent';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: dark.colors.background,
  },
});

const AddCollegePage = () => {
  const { updateUser } = useUpdateUserProfile();
  const { updateCurrentUser } = useSession();
  const { isMobile: isCompactDevice } = useMediaQuery();
  const { goBack } = useGoBack();

  const handleInstitutionSelect = useCallback(
    async (institution: any) => {
      if (!institution || !institution?.id) return;

      try {
        const response = await updateUser({ institutionId: institution?.id });
        if (response?.data?.updateUser) {
          showToast({
            type: TOAST_TYPE.SUCCESS,
            description: 'Institution updated successfully!',
          });
          updateCurrentUser?.({
            institutionId: institution?.id,
            institutionName: institution?.name,
          });
        } else {
          showToast({
            type: TOAST_TYPE.ERROR,
            description: 'Failed to update institution. Please try again.',
          });
        }
      } catch (error) {
        showToast({
          type: TOAST_TYPE.ERROR,
          description: `Error: ${error?.message || 'Could not update institution.'}`,
        });
      }
    },
    [updateUser, updateCurrentUser],
  );

  return (
    <View
      style={[styles.container, !isCompactDevice && { paddingHorizontal: 20 }]}
    >
      <SelectInstitutionComponent
        onInstitutionSelect={handleInstitutionSelect}
      />
    </View>
  );
};

export default React.memo(AddCollegePage);
