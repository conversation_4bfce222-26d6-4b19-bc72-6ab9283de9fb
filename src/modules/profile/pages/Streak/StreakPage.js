import React, { useCallback, useEffect, useState } from 'react';
import {
  Image,
  Platform,
  Pressable,
  ScrollView,
  Text,
  View,
} from 'react-native';
import { useRouter } from 'expo-router';
import InteractiveSecondaryButton from '@/src/components/atoms/InteractiveSecondaryButton';
import { ICON_TYPES } from '@/src/components/atoms/Icon';
import shieldIcon from '@/assets/images/icons/shield_icon.png';
import { MaterialIcons } from '@expo/vector-icons';
import { format } from 'date-fns';
import TextWithShadow from '@/src/components/shared/TextWithShadow';
import { closeBottomSheet, openBottomSheet } from 'molecules/BottomSheet/BottomSheet';
import streakRewardDisabledIcon from '@/assets/images/icons/streak_reward_disabled.png';
import { useSession } from '@/src/modules/auth/containers/AuthProvider';
import userReader from '@/src/core/readers/userReader';
import { GAME_MODE } from 'modules/games/constants/gameModes';
import WhatIsStreak from './components/WhatIsStreak';
import StreakCalendar from './components/StreakCalendar';
import useStreakAnalytics from '../../hooks/query/useStreakAnalytics';
import useStreakPageStyles from './StreakPage.style';
import Header from '../../../../components/shared/Header/Header';
import dark from '../../../../core/constants/themes/dark';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import StreakSharebleCard from 'modules/profile/pages/Streak/components/StreakSharableCard';
import { openShareableCardFlow } from 'shared/ShareResultModal';
import { closePopover, showPopover } from 'molecules/Popover/Popover';
import WebBackButton from 'shared/WebBackButton';
import Analytics from '@/src/core/analytics';
import { ANALYTICS_EVENTS } from '@/src/core/analytics/const';
import { formatTimeLeftinHMS, getTimeLeftUntilEndOfDayUTC } from '@/src/core/utils/getTimeLeftForEodUtc';


const StreakPage = () => {
  const styles = useStreakPageStyles();
  const { user } = useSession();
  const userName = userReader.username(user);
  const {
    currentStreakCount,
    hasStreak,
    streakFreezers,
    refreshStreakData,
    maxStreakCount,
  } = useStreakAnalytics();
  const { isMobile: isCompactDevice } = useMediaQuery();
  const isWeb = Platform.OS === 'web';

  const [timeLeft, setTimeLeft] = useState(getTimeLeftUntilEndOfDayUTC());

  useEffect(() => {
    const intervalId = setInterval(() => {
      setTimeLeft(getTimeLeftUntilEndOfDayUTC());
    }, 1000);
    return () => clearInterval(intervalId);
  }, []);

  const formattedTimeLeft = formatTimeLeftinHMS(timeLeft);

  const router = useRouter();

  const onPressPlay = useCallback(() => {
    router.replace(`/games?gameMode=${GAME_MODE.BLITZ}`);
  }, [router]);

  const onPressEarnShield = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.STREAKS.CLICKED_HOW_TO_EARN_STREAK_FREEZER)
    router.push(`/profile/${userName}/referral`);
  }, [router, userName]);

  const renderStreakShareCard = useCallback(
    () => (
      <View>
        <StreakSharebleCard streak={currentStreakCount} />
      </View>
    ),
    [currentStreakCount],
  );

  const handleShareStreakCard = useCallback(
    ({ message = '' } = EMPTY_OBJECT) => {
      Analytics.track(ANALYTICS_EVENTS.STREAKS.CLICKED_ON_SHARE_STREAK_CARD)
      if (isWeb) {
        showPopover({
          content: <StreakSharebleCard streak={currentStreakCount} />,
          overlayLook: true,
          animationType: 'slide',
        });
        return;
      }
      closePopover();
      openShareableCardFlow({
        renderResultCard: renderStreakShareCard,
        message,
        storyBackgroundColors: {
          backgroundBottomColor:
            dark.colors.game.share.storyBackgroundColorBottom,
          backgroundTopColor: dark.colors.game.share.storyBackgroundColorTop,
        },
      });
    },
    [renderStreakShareCard],
  );

  const renderShareButton = () => (
    <InteractiveSecondaryButton
      onPress={handleShareStreakCard}
      iconConfig={{
        type: ICON_TYPES.ENTYPO,
        name: 'share',
        size: 16,
        color: dark.colors.tertiary,
      }}
      buttonContainerStyle={{ width: 36, height: 36 }}
      buttonContentStyle={{ paddingHorizontal: 0, paddingVertical: 0 }}
    />
  );

  const renderCenterComponent = useCallback(
    () => (
      <Pressable onPress={onWhatsStreakPress}>
        <View style={styles.streakAvailableContainer}>
          <Image
            source={shieldIcon}
            style={{ width: 14, height: 14 }}
            resizeMode="contain"
          />
          <Text
            style={styles.streakAvailableText}
          >{`${streakFreezers ?? 0} Available`}</Text>
        </View>
      </Pressable>
    ),
    [
      styles.streakAvailableContainer,
      styles.streakAvailableText,
      streakFreezers,
    ],
  );

  const renderGainYourStreakCard = useCallback(
    () => (
      <View style={styles.playGameCard}>
        <View style={styles.timerContainer}>
          <MaterialIcons
            name="timer"
            color={dark.colors.streakOrangeColor}
            size={24}
          />
          <Text style={styles.timerText}>{`${formattedTimeLeft} (UTC)`}</Text>
        </View>
        <View style={styles.playContainer}>
          <Text style={styles.title}>
            Play a game to retain your day streak
          </Text>
          <InteractiveSecondaryButton
            label="PLAY"
            onPress={() => onPressPlay()}
            buttonContainerStyle={[styles.buttonContainerStyle, { height: 44 }]}
            labelStyle={styles.labelStyles}
            borderColor={dark.colors.streakOrangeColor}
          />
        </View>
      </View>
    ),
    [formattedTimeLeft, onPressPlay],
  );
  
  const onCloseBottomSheet = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.STREAKS.CLOSED_STREAK_FREEZER_BOTTOM_SHEET)
    closeBottomSheet();
  }, [closeBottomSheet]) 

  const onWhatsStreakPress = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.STREAKS.CLICKED_ON_STREAK_FREEZER_WIDGET)
    openBottomSheet({
      content: ({ closeBottomSheet }) => (
        <WhatIsStreak
          streakFreezers={streakFreezers}
          onPressEarnShield={onPressEarnShield}
          closeBottomSheet={() => onCloseBottomSheet(closeBottomSheet)}
        />
      ),
      styles: {
        frame: {
          borderTopColor: dark.colors.streakOrangeColor,
        },
      },
    });
  }, [onPressEarnShield, streakFreezers]);

  const render100DaysReward = () => (
    <View style={styles.rewardMainContainer}>
      <TextWithShadow
        text="MILESTONE REWARDS"
        textStyle={styles.streakShieldTitle}
        containerStyle={styles.streakTitleContainer}
        shadowColor="black"
        shadowOffsetX={0}
        shadowOffsetY={-6}
        strokeColor="#000000"
        strokeWidth={4}
      />
      <View style={styles.rewardContainer}>
        <Image
          source={streakRewardDisabledIcon}
          style={{ alignSelf: 'center' }}
        />
        <View style={styles.rewardContent}>
          <Text style={styles.days}>100 Days</Text>
          <Text style={styles.rewardDesc}>
            Reach a streak of 100 days to unlock this reward
          </Text>
          <Text style={styles.locked}>LOCKED</Text>
        </View>
      </View>
    </View>
  );

  return (
    <View style={[styles.container, !isCompactDevice && styles.webContainer]}>
      <Header
        isTransparentBg
        renderTrailingComponent={renderShareButton}
        renderCenterComponent={renderCenterComponent}
      />

      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={{ marginTop: 10 }}>
          <WebBackButton renderTrailingComponent={renderShareButton} />
        </View>
        <View
          style={[styles.contentContainer, !isCompactDevice && { height: 250 }]}
        >
          {!isCompactDevice && renderCenterComponent()}
          <TextWithShadow
            text={currentStreakCount}
            textStyle={
              hasStreak
                ? styles.streakCountStyle
                : styles.streakCountDisabledStyle
            }
            containerStyle={styles.streakCountContainer}
            shadowColor={
              hasStreak ? dark.colors.streakOrangeColor : dark.colors.primary
            }
            shadowOffsetX={isWeb ? -1 : -5}
            shadowOffsetY={isWeb ? 4 : -2}
            strokeColor="#000000"
            strokeWidth={6}
            shadowWidth={8}
          />
          <Text style={[styles.dayStreakText]}>DAY STREAK</Text>
          <View style={styles.maxStreakContainer}>
            <Text style={styles.maxStreak}>{maxStreakCount} DAY </Text>
            <Text style={styles.dayStreakText}>BEST STREAK</Text>
          </View>
        </View>
        {!hasStreak && renderGainYourStreakCard()}
        <View style={styles.calendarContainer}>
          <StreakCalendar />
        </View>
        <View style={styles.divider} />
        <View style={styles.rewardsSection}>{render100DaysReward()}</View>
      </ScrollView>
      {/* <View style={styles.shareStreakButton}>
        <View style={styles.footer}>
          <InteractiveSecondaryButton
            label={"WHAT'S A STREAK"}
            onPress={onWhatsStreakPress}
            buttonContainerStyle={[styles.buttonContainerStyle]}
            labelStyle={styles.labelStyles}
            borderColor={dark.colors.streakOrangeColor}
          />
        </View>
      </View> */}
    </View>
  );
};

export default React.memo(StreakPage);
