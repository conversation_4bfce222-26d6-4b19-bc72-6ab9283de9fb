import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    maxWidth: 600,
    width: '100%',
    height: '100%',
    flex: 1,
    backgroundColor: dark.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: dark.colors.tertiary,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Montserrat-700',
    color: dark.colors.textLight,
  },
  refreshButton: {
    padding: 8,
  },
  content: {
   
    flex: 1
  },
  currentShieldsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: dark.colors.primary,
    marginHorizontal: 16,
    marginTop: 16,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
    minHeight:100
  },
  shieldIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: dark.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  shieldIcon: {
    width: 30,
    height: 30,
    resizeMode: 'contain',
  },
  shieldInfoContainer: {
    flex: 1,
  },
  shieldCountText: {
    fontSize: 24,
    fontFamily: 'Montserrat-800',
    color: dark.colors.textLight,
  },
  shieldLabelText: {
    fontSize: 14,
    fontFamily: 'Montserrat-500',
    color: dark.colors.textDark,
  },
  transactionsContainer: {
    flex:1,
    marginTop: 24,
    paddingHorizontal: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontFamily: 'Montserrat-700',
    color: dark.colors.textLight,
    marginBottom: 12,
  },
  listContentContainer: {
    paddingBottom: 24,
  },
  transactionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: dark.colors.elevationPrimary,
    padding: 16,
    marginBottom: 12,
    borderBottomWidth: 0.5,
  },
  debitCard: {
    borderColor: dark.colors.tertiary,
  },
  creditCard: {
    borderColor: dark.colors.secondaryButtonBorder,
  },
  cardIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    backgroundColor: dark.colors.primary,
  },
  cardIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  userProfileImage: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  cardContent: {
    flex: 1,
  },
  cardTitle: {
    fontSize: 16,
    fontFamily: 'Montserrat-700',
    color: dark.colors.textLight,
  },
  cardSubtitle: {
    fontSize: 14,
    fontFamily: 'Montserrat-500',
    color: dark.colors.textDark,
    marginTop: 4,
  },
  cardDate: {
    fontSize: 12,
    fontFamily: 'Montserrat-400',
    color: dark.colors.textDark,
    marginTop: 2,
  },
  cardQuantity: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: dark.colors.primary,
  },
  quantityText: {
    fontSize: 16,
    fontFamily: 'Montserrat-700',
  },
  debitQuantity: {
    color: dark.colors.errorDark,
  },
  creditQuantity: {
    color: dark.colors.secondary,
  },
  emptyContainer: {
    padding: 24,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 14,
    fontFamily: 'Montserrat-500',
    color: dark.colors.textDark,
  },
});

export default styles;
