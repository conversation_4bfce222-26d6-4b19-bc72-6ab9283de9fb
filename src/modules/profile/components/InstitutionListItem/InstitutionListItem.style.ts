import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

export default StyleSheet.create({
  container: {
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 0.2,
    borderBottomColor: dark.colors.tertiary,
    backgroundColor: dark.colors.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  infoContainer: {
    flex: 1,
    marginLeft: 12,
    marginRight: 10,
  },
  logo: {
    width: 32,
    height: 32,
    borderRadius: 4,
    marginRight: 0,
  },
  nameText: {
    fontFamily: 'Montserrat-600',
    fontSize: 13,
    color: dark.colors.text,
    marginBottom: 4,
  },
  locationContainer: {
    marginTop: 2,
  },
  locationText: {
    fontSize: 12,
    fontFamily: 'Montserrat-500',
    color: dark.colors.textDark,
  },
});
