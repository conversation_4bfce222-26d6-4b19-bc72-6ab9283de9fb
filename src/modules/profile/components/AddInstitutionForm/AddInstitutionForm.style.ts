import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';
import { withOpacity } from 'core/utils/colorUtils';

export default StyleSheet.create({
  simplifiedContainer: {
    padding: 16,
    alignItems: 'center',
    backgroundColor: dark.colors.primary,
  },
  fullContainer: {
    padding: 16,
    gap: 16,
    minWidth: 300,
  },
  fieldContainer: {
    gap: 5,
  },
  titleText: {
    fontSize: 16,
    fontFamily: 'Montserrat-600',
    color: dark.colors.textLight,
    marginBottom: 16,
  },
  label: {
    fontSize: 12,
    color: dark.colors.textDark,
    fontFamily: 'Montserrat-600',
    letterSpacing: 0.15,
  },
  input: {
    backgroundColor: dark.colors.background,
    color: dark.colors.text,
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
    outlineStyle: 'none',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 13,
    width: '100%',
    height: 45,
    fontFamily: 'Montserrat-500',
  },
  helpText: {
    fontSize: 10,
    color: dark.colors.textDark,
    marginTop: 4,
  },
  errorText: {
    color: dark.colors.error,
    textAlign: 'center',
    marginBottom: 12,
    fontSize: 12,
  },
  button: {
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    marginTop: 8,
    minHeight: 44,
  },
  buttonPrimary: {
    backgroundColor: dark.colors.secondary,
  },
  buttonSecondary: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: dark.colors.border,
  },
  buttonTextPrimary: {
    color: dark.colors.primary,
    fontFamily: 'Montserrat-500',
    fontSize: 14,
  },
  buttonTextSecondary: {
    color: dark.colors.text,
    fontWeight: 'bold' as 'bold',
    fontSize: 14,
  },
  buttonDisabled: {
    opacity: 0.5,
  },
  typeSelectionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  typeButton: {
    flex: 1,
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 0.5,
    borderColor: dark.colors.tertiary,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 4,
  },
  typeButtonSelected: {
    backgroundColor: withOpacity(dark.colors.textLight, 0.1),
    borderColor: dark.colors.secondary,
  },
  typeButtonText: {
    fontSize: 12,
    fontFamily: 'Montserrat-600',
    color: dark.colors.textDark,
    letterSpacing: 0.15,
  },
  typeButtonTextSelected: {
    color: dark.colors.textLight,
  },
  logoSectionContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  logoPlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: dark.colors.placeholder,
    borderWidth: 1,
    borderColor: dark.colors.textLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  logoImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  uploadLogoText: {
    fontSize: 13,
    fontFamily: 'Montserrat-500',
    color: dark.colors.secondary,
    marginTop: 4,
  },
});
