import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import UserImage from 'atoms/UserImage';
import { View } from 'react-native';
import { FRIENDSHIP_STATUS } from 'modules/friendsAndFollowers/constants/friendshipStatus';
import useGetMessageGroupForFriends from '@/src/modules/friendsAndFollowers/hooks/queries/useGetMessageGroupForFriends';
import { useRouter } from 'expo-router';
import { showToast, TOAST_TYPE } from 'molecules/Toast';
import InteractiveSecondaryButton from '@/src/components/atoms/InteractiveSecondaryButton';
import { ICON_TYPES } from '@/src/components/atoms/Icon';
import FriendActionButton from 'modules/profile/components/UserProfileCard/components/FriendsActionButton';
import { openBottomSheet } from '@/src/components/molecules/BottomSheet/BottomSheet';
import { useSession } from 'modules/auth/containers/AuthProvider';
import SocialLinks from '../../SocialLinks';
import UserInfoCard from '../../UserInfoCard';
import ProfileShareCard from '../../ProfileShareCard';
import usePublicProfileCardController from '../../../hooks/usePublicProfileCardController';
import dark from '../../../../../core/constants/themes/dark';
import styles from './ExpandedUserProfileCard.style';

const ExpandedUserProfileCard = (props) => {
  const { user, isCurrentUser, userAdditionalInfo } = props;
  const { user: currentSessionUser } = useSession();

  const { isFollowing, friendshipStatus: friendshipStatusInitialValue } =
    userAdditionalInfo ?? EMPTY_OBJECT;

  const {
    isFollowing: isUserFollowedByMe,
    friendshipStatus,
    isCurrentUserIsGuest,
    isGuest,
    handleOnAddOrRemoveFriendPressed,
    navigateToFriendsPage,
    navigateToFindFriendsPage,
    handleOnChallengePressed,
  } = usePublicProfileCardController({
    user,
    isFollowing,
    friendshipStatus: friendshipStatusInitialValue,
  });

  const router = useRouter();
  const { getMessageGroupIDForFriends } = useGetMessageGroupForFriends();
  const routeToChat = useCallback(async () => {
    try {
      const { data } = await getMessageGroupIDForFriends({
        friendID: user?._id,
      });
      router.push(`/chat?messageGroupId=${data.getMessageGroupIdForFriends}`);
    } catch (e) {
      showToast({
        type: TOAST_TYPE.ERROR,
        description: `Something went wrong. Please try again later`,
      });
    }
  }, [getMessageGroupIDForFriends, user._id, router]);

  const handleShareProfile = useCallback(() => {
    openBottomSheet({
      content: ({ closeBottomSheet }) => <ProfileShareCard user={user} />,
    });
  }, [user]);

  return (
    <View style={styles.background}>
      <View style={styles.innerCard}>
        <View style={styles.imageContainer}>
          <UserImage
            user={user}
            size={48}
            rounded={false}
            style={styles.userImage}
          />
        </View>
        <UserInfoCard
          user={user}
          navigateToFriendsPage={navigateToFriendsPage}
        />
        <SocialLinks user={user} />
        {!isGuest && !isCurrentUserIsGuest && (
          <View style={styles.followAndFriendsButtonRow}>
            {!isCurrentUser && (
              <FriendActionButton
                friendshipStatus={friendshipStatus}
                onPress={handleOnAddOrRemoveFriendPressed}
              />
            )}
            {!isCurrentUser && (
              <InteractiveSecondaryButton
                label="CHALLENGE"
                labelStyle={styles.friendsStatusText}
                onPress={handleOnChallengePressed}
                buttonStyle={{ paddingHorizontal: 16 }}
                buttonContainerStyle={{
                  height: 38,
                  flex: 1,
                  width: '100%',
                  maxWidth: 200,
                }}
              />
            )}
            {isCurrentUser && (
              <InteractiveSecondaryButton
                label="ADD MORE FRIENDS"
                labelStyle={styles.friendsStatusText}
                iconConfig={{
                  name: 'adduser',
                  type: ICON_TYPES.ANT_DESIGN,
                  size: 20,
                  color: dark.colors.secondary,
                }}
                onPress={navigateToFindFriendsPage}
                buttonStyle={{ paddingHorizontal: 16 }}
                buttonContainerStyle={{ height: 38 }}
              />
            )}
            {friendshipStatus === FRIENDSHIP_STATUS.ACCEPTED ? (
              <InteractiveSecondaryButton
                iconConfig={{
                  name: 'mail',
                  type: ICON_TYPES.ENTYPO,
                  size: 20,
                  color: dark.colors.textLight,
                }}
                labelStyle={styles.friendsStatusText}
                onPress={routeToChat}
                buttonStyle={{ paddingHorizontal: 16 }}
                buttonContainerStyle={{ height: 38 }}
              />
            ) : null}
            <InteractiveSecondaryButton
              iconConfig={{
                name: 'share',
                type: ICON_TYPES.FEATHER,
                size: 20,
                color: dark.colors.textLight,
              }}
              labelStyle={styles.friendsStatusText}
              onPress={handleShareProfile}
              buttonStyle={{ paddingHorizontal: 16 }}
              buttonContainerStyle={{ height: 38 }}
            />
          </View>
        )}
      </View>
    </View>
  );
};

ExpandedUserProfileCard.propTypes = {
  user: PropTypes.object,
  badge: PropTypes.object,
  onPressBadge: PropTypes.func,
  shouldShowBadge: PropTypes.bool,
  onPressEditProfile: PropTypes.func,
  isCurrentUser: PropTypes.bool,
};

export default React.memo(ExpandedUserProfileCard);
