import { StyleSheet } from 'react-native';
import { withOpacity } from 'core/utils/colorUtils';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    gap: 12,
    paddingHorizontal: 16,
  },
  mainContainer: {
    backgroundColor: dark.colors.gradientBackground,
    borderRadius: 12,
    paddingVertical: 12,
  },
  linkItem: {
    width: 100,
    // maxWidth: 100,
    height: 24,
    borderLeftWidth: 0,
    borderRightWidth: 1,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: withOpacity(dark.colors.textLight, 0.4),
    borderRadius: 20,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
  },
  iconContainer: {
    backgroundColor: dark.colors.d3ButtonBorderDark,
    height: 24,
    width: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: withOpacity(dark.colors.textLight, 0.4),
  },
  linkText: {
    fontFamily: 'Montserrat-600',
    fontSize: 9,
    maxWidth: 64,
    color: withOpacity(dark.colors.textLight, 0.4),
    letterSpacing: 0.5,
  },
});

export default styles;
