import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

export default StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    backgroundColor: dark.colors.background,
  },
  input: {
    backgroundColor: dark.colors.primary,
    color: dark.colors.text,
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
    fontSize: 13,
    fontFamily: 'Montserrat-500',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    marginBottom: 12,
    height: 40,
    outlineStyle: 'none',
  },
  loader: {
    marginVertical: 16,
  },
  popoverImagePlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 8,
    backgroundColor: dark.colors.tertiary,
    marginBottom: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    color: dark.colors.error,
    textAlign: 'center',
    marginVertical: 16,
    fontSize: 13,
    fontFamily: 'Montserrat-400',
  },
  list: {
    flex: 1,
    borderRadius: 10,
    backgroundColor: dark.colors.background,
  },
  footerContainer: {
    backgroundColor: dark.colors.primary,
    borderBottomLeftRadius: 10,
    borderBottomRightRadius: 10,
    alignItems: 'center',
    paddingVertical: 16,
  },
  addNewText: {
    color: dark.colors.textDark,
    fontSize: 12,
    fontFamily: 'Montserrat-400',
  },
  emptyText: {
    color: dark.colors.textDark,
    textAlign: 'center',
    marginTop: 20,
    fontSize: 14,
    fontFamily: 'Montserrat-500',
  },
  confirmationContainer: {
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 'auto',
  },
  selectedInfoText: {
    color: dark.colors.text,
    fontSize: 13,
    fontFamily: 'Montserrat-500',
    flex: 1,
    marginRight: 10,
  },
  confirmButton: {
    backgroundColor: dark.colors.secondary,
    paddingVertical: 8,
    paddingHorizontal: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
    height: 40,
    width: '100%',
  },
  confirmButtonText: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'Montserrat-600',
  },
  headerExpanded: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 24,
    marginHorizontal: 16,
  },
  titleTextStyle: {
    color: 'white',
    fontSize: 17,
    fontFamily: 'Montserrat-500',
  },
});
