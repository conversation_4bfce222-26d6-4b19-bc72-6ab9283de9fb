import React, { useCallback, useState } from 'react';
import {
  ActivityIndicator,
  FlatList,
  Pressable,
  Text,
  TextInput,
  View,
} from 'react-native';
import {
  closeBottomSheet,
  openBottomSheet,
} from 'molecules/BottomSheet/BottomSheet';
import Header from '@/src/components/shared/Header';
import dark from 'core/constants/themes/dark';
import _size from 'lodash/size';
import _isEmpty from 'lodash/isEmpty';
import _trim from 'lodash/trim';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { closePopover, showPopover } from 'molecules/Popover/Popover';
import WebBackButton from 'shared/WebBackButton';
import ActionPopoverContent from '@/src/components/shared/ActionPopoverContent';
import _toUpper from 'lodash/toUpper';
import _isEqual from 'lodash/isEqual';
import _isFunction from 'lodash/isFunction';
import useInviteFriendOnMatiks from 'core/hooks/useInviteFriendOnMatiks';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import { useSession } from 'modules/auth/containers/AuthProvider';
import { router } from 'expo-router';
import userReader from '@/src/core/readers/userReader';
import DefaultCollegeIcon from 'atoms/DefaultCollegeIcon';
import useSearchInstitutions from '../../hooks/query/useSearchInstitutions';
import InstitutionListItem from '../InstitutionListItem';
import AddInstitutionForm from '../AddInstitutionForm';
import styles from './SelectInstitutionComponent.style';

const SelectInstitutionComponent = ({
  onInstitutionSelect,
}: {
  onInstitutionSelect: Function;
}) => {
  const [selectedForPopover, setSelectedForPopover] = useState<any>(null);
  const { user } = useSession();
  const {
    searchTerm,
    setSearchTerm,
    institutions,
    loading: searchLoading,
    error: searchError,
  } = useSearchInstitutions();

  const { isMobile: isCompactDevice } = useMediaQuery();

  const handleConfirmJoin = useCallback(
    (institution: any) => {
      if (onInstitutionSelect) {
        onInstitutionSelect?.(institution).then(() => {
          closePopover();
          setSelectedForPopover(null);
          router.push(
            `/profile/${userReader.username(user)}/add-college-friends`,
          );
        });
      }
    },
    [onInstitutionSelect, user],
  );

  const handleSelectAndShowJoinPopover = useCallback(
    (institution: any) => {
      setSelectedForPopover(institution);
      showPopover({
        content: (
          <ActionPopoverContent
            iconPlaceholder
            renderIconPlaceholder={() => (
              <View style={styles.popoverImagePlaceholder}>
                <DefaultCollegeIcon size={40} />
              </View>
            )}
            title={`JOIN ${_toUpper(_trim(institution?.name) || 'INSTITUTION')} ?`}
            message="This will be shown on your profile page."
            primaryActionText="OKAY, CONFIRM"
            onPrimaryAction={() => handleConfirmJoin(institution)}
            secondaryActionText="NO, I DONT WANT THIS"
            onSecondaryAction={() => {
              closePopover();
              setSelectedForPopover(null);
            }}
          />
        ),
        overlayLook: true,
        style: {
          padding: 0,
          margin: 0,
          borderRadius: 12,
          borderWidth: 0,
          backgroundColor: 'transparent',
        },
        backdropStyle: { backgroundColor: 'rgba(0,0,0,0.6)' },
      });
    },
    [handleConfirmJoin],
  );

  const { handleNormalShare } = useInviteFriendOnMatiks();

  const handleOnInviteFriendsPress = useCallback(() => {
    closePopover();
    handleNormalShare({
      eventToBeTracked:
        ANALYTICS_EVENTS.PROFILE.INVITE_FRIENDS_BUTTON_AFTER_COLLEGE_CREATION,
    });
  }, [handleNormalShare]);

  const handleInstitutionCreated = useCallback(
    (newlyCreatedInstitution: any) => {
      closeBottomSheet();
      if (_isFunction(onInstitutionSelect)) {
        onInstitutionSelect?.(newlyCreatedInstitution);
      }
      showPopover({
        content: (
          <ActionPopoverContent
            iconPlaceholder
            renderIconPlaceholder={() => (
              <View style={styles.popoverImagePlaceholder}>
                <DefaultCollegeIcon size={40} />
              </View>
            )}
            title="INSTITUTION CREATED"
            titleColor={dark.colors.streak}
            message={`${_trim(newlyCreatedInstitution?.name) || 'Your college'} has been created. Invite your friends to join the group`}
            primaryActionText="INVITE FRIENDS"
            onPrimaryAction={handleOnInviteFriendsPress}
            secondaryActionText="DO IT LATER"
            onSecondaryAction={closePopover}
          />
        ),
        overlayLook: true,
        style: {
          padding: 0,
          margin: 0,
          borderRadius: 12,
          borderWidth: 0,
          backgroundColor: 'transparent',
        },
        backdropStyle: { backgroundColor: 'rgba(0,0,0,0.7)' },
      });
    },
    [handleOnInviteFriendsPress, onInstitutionSelect],
  );

  const handleAddNewClick = useCallback(() => {
    openBottomSheet({
      content: (
        <AddInstitutionForm onInstitutionCreated={handleInstitutionCreated} />
      ),
    });
  }, [handleInstitutionCreated]);

  const renderItem = ({ item }: { item: any }) => (
    <InstitutionListItem
      institution={item}
      onPress={() => handleSelectAndShowJoinPopover(item)}
      isSelected={_isEqual(selectedForPopover?.id, item.id)}
    />
  );

  const renderListFooter = () => {
    if (_size(searchTerm) < 3 && _isEmpty(institutions)) return null;
    return (
      <View style={styles.footerContainer}>
        <Pressable onPress={handleAddNewClick}>
          <Text style={styles.addNewText}>
            Not in the list?{'  '}
            <Text style={{ color: dark.colors.secondary }}>Click here</Text>
          </Text>
        </Pressable>
      </View>
    );
  };

  return (
    <View style={{ flex: 1 }}>
      <Header title="Add your institution" />
      {!isCompactDevice && (
        <View style={styles.headerExpanded}>
          <WebBackButton title="Add your institution" />
        </View>
      )}
      <View style={styles.container}>
        <TextInput
          style={styles.input}
          placeholder="Search for your College / School"
          placeholderTextColor={dark.colors.placeholder}
          value={searchTerm}
          onChangeText={setSearchTerm}
        />

        {searchLoading && (
          <ActivityIndicator
            style={styles.loader}
            size="small"
            color={dark.colors.secondary}
          />
        )}

        {searchError && (
          <Text style={styles.errorText}>
            Error searching: {searchError?.message}
          </Text>
        )}

        <FlatList
          style={styles.list}
          data={institutions}
          renderItem={renderItem}
          keyExtractor={(item) => item.id}
          ListFooterComponent={renderListFooter}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        />
      </View>
    </View>
  );
};

export default React.memo(SelectInstitutionComponent);
