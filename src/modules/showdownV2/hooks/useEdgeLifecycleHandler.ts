/* eslint-disable no-undef */
/* eslint-disable no-param-reassign */
import useShowdownStore from 'store/useShowdownStore';
import { useEffect, useRef } from 'react';
import showdownReader from 'modules/showdownV2/readers/showdownReader';
import useCleanTimeoutRefs from './useCleanTimeoutRefs';

const useEdgeLifecycleHandler = () => {
  const { updateOtherStates, startTime, endTime, registrationEndTime } =
    useShowdownStore((state) => ({
      updateOtherStates: state.updateOtherStates,
      startTime: showdownReader.startTime(state.showdown) ?? '',
      endTime: showdownReader.endTime(state.showdown) ?? '',
      registrationEndTime:
        showdownReader.registrationEndTime(state.showdown) ?? '',
    }));

  const updateOtherStatesRef = useRef(updateOtherStates);
  updateOtherStatesRef.current = updateOtherStates;

  const startTimerRef = useRef<NodeJS.Timeout | null>(null);
  const endTimerRef = useRef<NodeJS.Timeout | null>(null);
  const registrationEndsAtRef = useRef<NodeJS.Timeout | null>(null);

  const runOnceRef = useRef(false);

  useEffect(() => {
    if (runOnceRef.current) return;
    if (!startTime || !endTime || !registrationEndTime) return;
    runOnceRef.current = true;
    const currentTime = getCurrentTime();

    /**
     * Registration End Time scheduledUpdate
     */
    const timeUntilRegistrationEnds =
      new Date(registrationEndTime).getTime() - currentTime;
    registrationEndsAtRef.current = setTimeout(() => {
      updateOtherStatesRef.current((otherStates: any) => {
        otherStates.hasRegistrationEnded = true;
      });
    }, timeUntilRegistrationEnds);

    /**
     * Showdown Start Time scheduledUpdate
     */
    const timeUntilStart = new Date(startTime).getTime() - currentTime;
    const timeUntilEnd = new Date(endTime).getTime() - currentTime;
    if (timeUntilEnd > 0) {
      startTimerRef.current = setTimeout(() => {
        updateOtherStatesRef.current((otherStates: any) => {
          otherStates.isLive = true;
        });
      }, timeUntilStart);
    }
    endTimerRef.current = setTimeout(() => {
      updateOtherStatesRef.current((otherStates: any) => {
        otherStates.hasEnded = true;
        otherStates.isLive = false;
      });
    }, timeUntilEnd);
    return () => {
      runOnceRef.current = false;
    };
  }, [registrationEndTime, startTime, endTime]);

  useCleanTimeoutRefs(registrationEndsAtRef, startTimerRef, endTimerRef);
};

export default useEdgeLifecycleHandler;
