/* eslint-disable no-use-before-define */
/* eslint-disable no-param-reassign */
/* eslint-disable no-undef */
/* eslint-disable react-hooks/exhaustive-deps */
import { MutableRefObject, useCallback, useEffect, useRef } from 'react';
import useShowdownStore from 'store/useShowdownStore';
import showdownReader from '../readers/showdownReader';
import useCleanTimeoutRefs from './useCleanTimeoutRefs';

const useShowdownRoundLifecycle = (
  fetchDataRef: MutableRefObject<() => Promise<void>>,
) => {
  const {
    updateOtherStates,
    startTime,
    totalRounds,
    gapBwRounds,
    roundTime,
    fetchFixtures,
    maxWaitTime,
  } = useShowdownStore((state) => ({
    updateOtherStates: state.updateOtherStates,
    startTime: showdownReader.startTime(state.showdown) ?? '',
    totalRounds: showdownReader.roundsCount(state.showdown) ?? 0,
    gapBwRounds: showdownReader.gapBwRounds(state.showdown) ?? 0,
    roundTime: showdownReader.roundTime(state.showdown) ?? 0,
    fetchFixtures: state.fetchFixtures,
    maxWaitTime: showdownReader.maxWaitTime(state.showdown) ?? 0,
  }));

  const fetchFixturesRef = useRef(fetchFixtures);
  fetchFixturesRef.current = fetchFixtures;
  const updateOtherStatesRef = useRef(updateOtherStates);
  updateOtherStatesRef.current = updateOtherStates;

  const shouldRefetchData = useRef(false);
  shouldRefetchData.current = false;

  useEffect(() => {
    if (shouldRefetchData.current) {
      fetchDataRef.current();
      shouldRefetchData.current = false;
    }
  }, []);

  const breakEndsAtTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const roundEndsAtTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const startTimerRef = useRef<NodeJS.Timeout | null>(null);
  const joinUntilTimestampTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const runOnceRef = useRef(false);

  const updateRoundInternalInfo = useCallback(() => {
    const currentTime = getCurrentTime();
    if (!startTime || !gapBwRounds || !roundTime || !totalRounds) return;
    const startDateTime = new Date(startTime).getTime();
    const roundTimeInMs = roundTime * 1000;
    const gapBwRoundsInMs = gapBwRounds * 1000;
    const totalRoundDuration = gapBwRoundsInMs + roundTimeInMs;

    const currentRound = Math.min(
      totalRounds,
      Math.ceil((currentTime - startDateTime) / totalRoundDuration),
    );
    const currentRoundTime = (currentTime - startDateTime) % totalRoundDuration;
    const isBreak = currentRoundTime > roundTimeInMs;
    const roundEndsAt =
      startDateTime +
      currentRound * roundTimeInMs +
      (currentRound - 1) * gapBwRoundsInMs;
    const breakEndsAt = roundEndsAt + gapBwRoundsInMs;
    const joinUntilTimestamp = roundEndsAt - roundTimeInMs + maxWaitTime * 1000;

    if (currentRound > totalRounds) {
      updateOtherStatesRef.current((otherStates: any) => {
        otherStates.currentRound = totalRounds;
        otherStates.isBreak = false;
        otherStates.isLive = false;
        otherStates.hasEnded = true;
      });
      return;
    }

    updateOtherStatesRef.current((otherStates: any) => {
      otherStates.currentRound = currentRound;
      otherStates.isBreak = isBreak;
      otherStates.roundEndsAt = roundEndsAt;
      otherStates.breakEndsAt = breakEndsAt;
      otherStates.joinUntilTimestamp = joinUntilTimestamp;
      otherStates.canUserJoin = joinUntilTimestamp > currentTime;
      otherStates.isRoundActive = !isBreak;
    });

    const oldRoundEndsAtTimeoutRef = roundEndsAtTimeoutRef.current;
    if (oldRoundEndsAtTimeoutRef) clearTimeout(oldRoundEndsAtTimeoutRef);
    roundEndsAtTimeoutRef.current = setTimeout(() => {
      updateOtherStatesRef.current((otherStates: any) => {
        otherStates.isBreak = true;
        otherStates.isRoundActive = false;
      });
    }, roundEndsAt - currentTime);

    const oldJoinUntilTimestampTimeoutRef =
      joinUntilTimestampTimeoutRef.current;
    if (oldJoinUntilTimestampTimeoutRef)
      clearTimeout(oldJoinUntilTimestampTimeoutRef);
    if (currentTime < joinUntilTimestamp) {
      joinUntilTimestampTimeoutRef.current = setTimeout(() => {
        updateOtherStatesRef.current((otherStates: any) => {
          otherStates.canUserJoin = false;
        });
      }, joinUntilTimestamp - currentTime);
    }

    const oldBreakEndsAtTimeoutRef = breakEndsAtTimeoutRef.current;
    if (oldBreakEndsAtTimeoutRef) clearTimeout(oldBreakEndsAtTimeoutRef);
    breakEndsAtTimeoutRef.current = setTimeout(() => {
      if (currentRound < totalRounds) {
        updateRoundInternalInfoRef.current();
      } else if (breakEndsAtTimeoutRef.current)
        clearTimeout(breakEndsAtTimeoutRef.current);
    }, breakEndsAt - currentTime);
  }, [startTime, totalRounds, gapBwRounds, roundTime, maxWaitTime]);

  const updateRoundInternalInfoRef = useRef(updateRoundInternalInfo);
  updateRoundInternalInfoRef.current = updateRoundInternalInfo;

  useEffect(() => {
    if (!startTime || !gapBwRounds || !roundTime || !totalRounds) return;
    if (runOnceRef.current) return;
    runOnceRef.current = true;
    startTimerRef.current = setTimeout(
      () => {
        updateRoundInternalInfoRef.current();
      },
      new Date(startTime).getTime() - getCurrentTime(),
    );

    return () => {
      runOnceRef.current = false;
    };
  }, [startTime, totalRounds, gapBwRounds, roundTime]);

  useCleanTimeoutRefs(roundEndsAtTimeoutRef, breakEndsAtTimeoutRef);
};

export default useShowdownRoundLifecycle;
