import { useMemo } from 'react';

import dark from 'core/constants/themes/dark';
import useShowdownStore from '@/src/store/useShowdownStore';
import _get from 'lodash/get';
import { SHOWDOWN_PLAYER_STATUS } from '../constants/showdownPlayerStatus';
import showdownReader from '../readers/showdownReader';

const useUserCantPlay = () => {
  const {
    isLive,
    playerStatus,
    breakEndsAt,
    isRoundActive,
    totalRounds,
    canUserJoin,
    currentRound,
    hasJoined,
  } = useShowdownStore((state) => ({
    isLive: state.otherStates.isLive,
    playerStatus: showdownReader.playerStatus(state.showdown),
    breakEndsAt: state.otherStates.breakEndsAt,
    isRoundActive: state.otherStates.isRoundActive,
    totalRounds: showdownReader.roundsCount(state.showdown),
    canUserJoin: state.otherStates.canUserJoin,
    currentRound: state.otherStates.currentRound,
    hasJoined: _get(
      state.showdown,
      ['currentUserParticipation', 'currentRound', 'hasJoined'],
      false,
    ),
  }));

  return useMemo(() => {
    if (!isLive && playerStatus !== SHOWDOWN_PLAYER_STATUS.BYE) {
      return null;
    }
    if (playerStatus === SHOWDOWN_PLAYER_STATUS.BYE) {
      return {
        title: 'You’ve Been Awarded a Bye!',
        color: dark.colors.secondary,
        desc: `You’ve been awarded a bye this round and earn 1 point. Your next match starts in `,
        timer: breakEndsAt,
      };
    }

    if (playerStatus === SHOWDOWN_PLAYER_STATUS.OPPONENT_ABSENT) {
      return {
        title: 'You won by default!',
        color: dark.colors.secondary,
        desc: `Your opponent didn’t show up, and you’ve been awarded 1 point. Next round starts in `,
        timer: breakEndsAt,
      };
    }

    if (!isRoundActive) {
      return null;
    }

    if (playerStatus === SHOWDOWN_PLAYER_STATUS.ROUND_COMPLETED) {
      return null;
    }

    if (!canUserJoin) {
      if (hasJoined) {
        return null;
      }
      if (currentRound !== totalRounds) {
        if (playerStatus === SHOWDOWN_PLAYER_STATUS.BOTH_DID_NOT_PLAY) {
          return {
            title: 'You missed the match!',
            color: dark.colors.lightRed,
            desc: `You both missed the match. Your next match starts in `,
            timer: breakEndsAt,
          };
        }
        return {
          title: 'You missed the match!',
          color: dark.colors.lightRed,
          desc: `Your opponent wins, and you receive 0 points. Don't worry, you can join next round in `,
          timer: breakEndsAt,
        };
      }
      return {
        title: 'You missed the match!',
        color: dark.colors.lightRed,
        desc: `Your opponent wins, and you receive 0 points!`,
      };
    }
    return null;
  }, [
    isLive,
    playerStatus,
    isRoundActive,
    canUserJoin,
    currentRound,
    breakEndsAt,
    totalRounds,
    hasJoined,
  ]);
};

export default useUserCantPlay;
