import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import { useMemo } from 'react';

const styles = StyleSheet.create({
  lockedRegisterButton: {
    backgroundColor: dark.colors.tertiary,
    padding: 10,
    height: 36,
    width: '100%',
    borderRadius: 5,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    gap: 8,
  },
  lockedRegisterText: {
    color: dark.colors.text,
    fontSize: 14,
    fontFamily: 'Montserrat-600',
  },
  container: {
    flex: 1,
    backgroundColor: 'transparent',
    padding: 20,
  },
  iconContainer: {
    backgroundColor: dark.colors.tertiary,
    padding: 10,
    borderRadius: 10,
  },
  iconStyle: {
    height: 20,
    width: 20,
  },
  infoContainer: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    gap: 15,
    padding: 20,
    borderWidth: 2,
    borderColor: dark.colors.tertiary,
    borderRadius: 10,
  },

  icon: {
    fontSize: 24,
    color: '#FFF',
  },
  info: {
    gap: 4,
    display: 'flex',
    alignContent: 'center',
    justifyContent: 'center',
    flexDirection: 'column',
  },
  infoText: {
    fontFamily: 'Montserrat-500',
    lineHeight: 12,
    color: dark.colors.textDark,
    // marginTop: 5,
  },
  eligibilityText: {
    fontFamily: 'Montserrat-500',
    lineHeight: 16,
    fontSize: 14,
    color: dark.colors.textDark,
  },
  eligibilityTextHeading: {
    fontFamily: 'Montserrat-500',
    lineHeight: 12,
    fontSize: 16,
    color: dark.colors.textDark,
  },
  infoNumber: {
    lineHeight: 12,
    color: '#FFF',
    fontSize: 14,
    marginTop: 5,
  },
  eligibilityContainer: {
    marginTop: 20,
    backgroundColor: 'transparent',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  mobileRoundedButton: {
    borderRadius: 50,
    height: 40,
  },
  unRegisterButton: {
    backgroundColor: dark.colors.primary,
    paddingVertical: 10,
    borderRadius: 10,
    alignItems: 'center',
  },
  unregisterText: {
    fontFamily: 'Montserrat-600',
    fontSize: 13,
    lineHeight: 20,
    color: '#FF7777',
  },
  buttonText: {
    color: '#000',
    fontSize: 16,
    fontFamily: 'Montserrat-700',
  },
  registerButton: {
    backgroundColor: dark.colors.secondary,
    padding: 10,
    height: 36,
    width: '100%',
    borderRadius: 5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  inactiveButton: {
    backgroundColor: dark.colors.tertiary,
    padding: 10,
    height: 36,
    width: '100%',
    borderRadius: 5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  contestStartedText: {
    color: dark.colors.textDark,
    fontSize: 14,
    textAlign: 'center',
    fontFamily: 'Montserrat-600',
  },
  buttonBox: {
    width: '100%',
    alignContent: 'center',
    alignItems: 'center',
    justifyContent: 'center',
  },
  registerText: {
    color: dark.colors.card,
    fontSize: 16,
    fontFamily: 'Montserrat-600',
  },
  inactiveButtonText: {
    color: dark.colors.textDark,
    fontSize: 16,
    fontFamily: 'Montserrat-600',
  },
});

export default styles;

export const useUnRegisteredButtonStyles = () => {
  const { isMobile } = useMediaQuery();
  const buttonStyles = useMemo(
    () => ({
      lockedRegisterButton: {
        backgroundColor: dark.colors.tertiary,
        padding: 10,
        height: isMobile ? 40 : 36,
        width: '100%',
        borderRadius: isMobile ? 50 : 8,
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'row',
        gap: 8,
      },
      registerButton: {
        backgroundColor: dark.colors.secondary,
        padding: 10,
        height: isMobile ? 40 : 36,
        width: '100%',
        borderRadius: isMobile ? 50 : 8,
        alignItems: 'center',
        justifyContent: 'center',
      },
      unRegisterButton: {
        backgroundColor: dark.colors.primary,
        paddingVertical: 10,
      },
      unregisterText: {
        fontFamily: 'Montserrat-600',
        fontSize: 13,
        lineHeight: 20,
        color: '#FF7777',
      },
      inactiveButtonText: {
        color: dark.colors.textDark,
        fontSize: isMobile ? 14 : 16,
        fontFamily: 'Montserrat-600',
      },
      inactiveButton: {
        backgroundColor: dark.colors.tertiary,
        padding: 10,
      },
      registerText: {
        color: dark.colors.card,
        fontSize: isMobile ? 14 : 16,
        fontFamily: 'Montserrat-600',
      },
    }),
    [isMobile],
  );

  return buttonStyles;
};

export const useRegisteredButtonStyles = () => {
  const { isMobile } = useMediaQuery();
  const buttonStyles = useMemo(
    () => ({
      button: {
        borderRadius: isMobile ? 50 : 8,
        height: isMobile ? 40 : 36,
        width: '100%',
        alignItems: 'center',
        justifyContent: 'center',
      },
      unRegisterButton: {
        backgroundColor: dark.colors.primary,
        paddingVertical: 10,
      },
      joinGameButton: {
        borderRadius: isMobile ? 50 : 8,
        height: isMobile ? 40 : 36,
        width: '100%',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: dark.colors.secondary,
      },
      joinGameButtonText: {
        color: dark.colors.card,
        fontSize: isMobile ? 14 : 16,
        fontFamily: 'Montserrat-600',
      },
      unregisterText: {
        fontFamily: 'Montserrat-600',
        fontSize: 13,
        lineHeight: 20,
        color: '#FF7777',
      },
      inactiveButtonText: {
        color: dark.colors.textDark,
        fontSize: isMobile ? 14 : 16,
        fontFamily: 'Montserrat-600',
      },
      registerButton: {
        backgroundColor: dark.colors.secondary,
        padding: 10,
        width: '100%',
      },
      inactiveButton: {
        backgroundColor: dark.colors.tertiary,
        padding: 10,
        borderRadius: isMobile ? 50 : 8,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      },
      registerText: {
        color: dark.colors.card,
        fontSize: isMobile ? 14 : 16,
        fontFamily: 'Montserrat-600',
      },
    }),
    [isMobile],
  );

  return buttonStyles;
};
