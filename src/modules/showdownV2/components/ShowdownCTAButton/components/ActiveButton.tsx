import { TouchableOpacity, Text } from 'react-native';
import React from 'react';
import useCountDownTimer from 'modules/showdownV2/hooks/useCountDownTimer';

const TextWithTimer = React.memo(
  ({
    text,
    timer,
    textStyle,
  }: {
    text: string;
    timer: number;
    textStyle: any;
  }) => {
    const { formattedTime } = useCountDownTimer({ targetTimeStamp: timer });
    return (
      <Text style={textStyle}>
        {text} ({formattedTime})
      </Text>
    );
  },
);

const ActiveButton = ({
  label,
  buttonStyle,
  textStyle,
  timer = 0,
  onPress,
}: {
  label: string;
  buttonStyle: any;
  textStyle: any;
  timer: number;
  onPress: () => void;
}) => (
  <TouchableOpacity style={buttonStyle} onPress={() => onPress()}>
    {timer && timer > 0 ? (
      <TextWithTimer text={label} timer={timer} textStyle={textStyle} />
    ) : (
      <Text style={textStyle}>{label}</Text>
    )}
  </TouchableOpacity>
);

export default React.memo(ActiveButton);
