import { StyleSheet } from 'react-native';
import dark from '../../../../core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    width: 'auto',
  },
  headerTitle: {
    color: dark.colors.textDark,
    fontSize: 12,
    lineHeight: 20,
    fontFamily: 'Montserrat-500',
    borderRightWidth: 1,
    borderColor: dark.colors.tertiary,
    width: 201,
    paddingTop: 6,
    paddingBottom: 14,
    paddingLeft: 6,
  },
  NoDataContainer: {
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
  },
  noDataText: {
    color: dark.colors.textDark,
    fontSize: 14,
    lineHeight: 20,
    fontFamily: 'Montserrat-500',
  },
  headerRoundTitle: {
    paddingTop: 6,
    paddingBottom: 14,
    paddingLeft: 6,
    width: 70,
    color: dark.colors.textDark,
    fontSize: 12,
    lineHeight: 20,
    fontFamily: 'Montserrat-500',
  },
  row: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderColor: dark.colors.tertiary,
    flex: 1,
    width: 'auto',
  },
  headerRow: {
    borderBottomWidth: 1,
    borderColor: dark.colors.tertiary,
    paddingLeft: 32,
    marginTop: 8,
  },
  cell: {
    justifyContent: 'center',
    alignItems: 'center',
    height: 54,
    borderColor: dark.colors.tertiary,
  },
  rankCell: {
    width: 32,
  },
  scoreCell: {
    width: 70,
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    paddingTop: 6,
    paddingLeft: 6,
  },
  nameCell: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 2,
    justifyContent: 'flex-start',
    columnGap: 8,
    borderRightWidth: 1,
    width: 201,
    paddingVertical: 7,
    paddingHorizontal: 6,
  },
  rankText: {
    color: 'white',
    fontSize: 12,
    lineHeight: 20,
    fontFamily: 'Montserrat-500',
  },
  scoreText: {
    color: dark.colors.secondary,
    fontSize: 12,
    lineHeight: 20,
    fontFamily: 'Montserrat-500',
  },
  name: {
    fontSize: 14,
    lineHeight: 20,
    color: 'white',
    fontFamily: 'Montserrat-400',
  },
  ratingText: {
    fontSize: 12,
    lineHeight: 20,
    color: dark.colors.textDark,
  },
});

export default styles;
