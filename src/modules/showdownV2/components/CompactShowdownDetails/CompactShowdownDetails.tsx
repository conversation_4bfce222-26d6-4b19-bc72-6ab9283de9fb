import React, { useCallback, useMemo } from 'react';
import { Image, Platform, StatusBar, Text, View } from 'react-native';
import Header from '@/src/components/shared/Header';
import LinearGradient from 'atoms/LinearGradient';
import dark from '@/src/core/constants/themes/dark';
import ShowdownLogo from 'assets/images/showdown_logo.png';
import useShowdownStore from 'store/useShowdownStore';
import styles from './CompactShowdownDetails.style';
import ShowdownCTAButton from '../ShowdownCTAButton/ShowdownCTAButton';
import showdownReader from '../../readers/showdownReader';
import ShowdownCompactTabBar from '../ShowdownCompactTabBar';

const CompactShowdownDetails = () => {
  const isNativeDevice = Platform.OS !== 'web';
  const { startTime, endTime, name, roundsCount, isLive } = useShowdownStore(
    (state) => ({
      startTime: state.showdown
        ? showdownReader.startTime(state.showdown)
        : null,
      endTime: state.showdown ? showdownReader.endTime(state.showdown) : null,
      name: state.showdown ? showdownReader.name(state.showdown) : null,
      roundsCount: state.showdown
        ? showdownReader.roundsCount(state.showdown) ?? 0
        : 0,
      isLive: state.otherStates.isLive,
    }),
  );

  const renderJoinNowOrRegisterButton = useMemo(
    () => (
      <View style={styles.CTAContainer}>
        <ShowdownCTAButton />
      </View>
    ),
    [],
  );

  const showdownInfoHeader = useCallback(
    () => (
      <View style={styles.contestDetails}>
        <View style={styles.gradientBox}>
          <Image source={ShowdownLogo} style={styles.iconContainer} />
        </View>
        <View style={styles.contestTimeAndDesc}>
          <View style={styles.detailsRow}>
            <Text style={styles.contestTitle} numberOfLines={1}>
              {name}
            </Text>
            {isLive ? (
              <View style={styles.liveContainer}>
                <Text style={styles.liveText}>Live</Text>
              </View>
            ) : null}
          </View>
          <Text style={styles.hostedBy}>Hosted By Matiks</Text>
          {startTime ? (
            <Text style={styles.detailsText}>
              {new Date(startTime).toLocaleDateString('en-GB', {
                day: 'numeric',
                month: 'short',
              })}{' '}
              |{' '}
              {new Date(startTime).toLocaleTimeString('en-US', {
                hour: 'numeric',
                minute: 'numeric',
              })}{' '}
              -{' '}
              {new Date(endTime).toLocaleTimeString('en-US', {
                hour: 'numeric',
                minute: 'numeric',
              })}{' '}
              | {`${roundsCount} rounds`}
            </Text>
          ) : null}
        </View>
      </View>
    ),
    [isLive, name, startTime, endTime, roundsCount],
  );

  return (
    <View style={styles.container}>
      <StatusBar
        translucent
        barStyle="light-content"
        backgroundColor="#A78A30C2"
      />
      {isNativeDevice ? (
        <LinearGradient
          colors={dark.colors.showdownDetailPageGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 0.55 }}
          style={styles.gradient}
        />
      ) : null}
      <View style={styles.innerContainer}>
        <Header title="Showdown" isTransparentBg />
        {showdownInfoHeader()}
      </View>
      <View style={{ flex: 1, marginBottom: 60 }}>
        <ShowdownCompactTabBar />
      </View>
      {renderJoinNowOrRegisterButton}
    </View>
  );
};

export default React.memo(CompactShowdownDetails);
