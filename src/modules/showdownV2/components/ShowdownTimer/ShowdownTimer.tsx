/* eslint-disable react/require-default-props */
import React from 'react';
import { View, Text, Image } from 'react-native';
import useShowdownStore from 'store/useShowdownStore';
import useMediaQuery from 'core/hooks/useMediaQuery';
import timerIcon from 'assets/images/timer.png';
import showdownReader from 'modules/showdownV2/readers/showdownReader';
import useCountDownTimer from 'modules/showdownV2/hooks/useCountDownTimer';
import { SHOWDOWN_PLAYER_STATUS } from 'modules/showdownV2/constants/showdownPlayerStatus';
import _get from 'lodash/get';
import useStyles from './ShowdownTimer.style';

const Timer = React.memo(({ timer }: { timer: number }) => {
  const { isMobile } = useMediaQuery();
  const { formattedTime } = useCountDownTimer({
    targetTimeStamp: timer,
  });
  const styles = useStyles();
  return (
    <Text style={[styles.infoNumber, isMobile && styles.infoNumberMobile]}>
      {formattedTime}
    </Text>
  );
});

const TimerComponent = React.memo(
  ({
    title,
    timerEndsAt = -1,
    description = '',
  }: {
    title: string;
    timerEndsAt?: number;
    description?: string;
  }) => {
    const styles: any = useStyles();
    // Determine what to render based on props
    let contentToRender = null;
    if (timerEndsAt > 0) {
      contentToRender = <Timer timer={timerEndsAt} />;
    } else if (description) {
      contentToRender = <Text style={styles.infoNumber}>{description}</Text>;
    }
    return (
      <View style={styles.infoItem}>
        <View style={styles.iconContainer}>
          <Image source={timerIcon} style={styles.iconStyle} />
        </View>
        <View style={styles.info}>
          <Text style={styles.infoText}>{title}</Text>
          {contentToRender}
        </View>
      </View>
    );
  },
);

const ShowdownRightPaneTimer = React.memo(() => {
  const {
    hasEnded,
    isLive,
    startTime,
    endTime,
    currentRound,
    roundEndsAt,
    breakEndsAt,
    totalRounds,
    isBreak,
    canUserJoin,
    playerStatus,
    hasRegistrationEnded,
    hasUserRegistered,
    registrationEndTime,
    hasJoined,
  } = useShowdownStore((state) => ({
    canUserJoin: state.otherStates.canUserJoin,
    hasEnded: state.otherStates.hasEnded ?? false,
    isLive: state.otherStates.isLive ?? false,
    startTime: showdownReader.startTime(state.showdown),
    endTime: showdownReader.endTime(state.showdown),
    currentRound: state.otherStates.currentRound,
    roundEndsAt: state.otherStates.roundEndsAt,
    breakEndsAt: state.otherStates.breakEndsAt,
    totalRounds: showdownReader.roundsCount(state.showdown),
    isBreak: state.otherStates.isBreak ?? false,
    playerStatus: showdownReader.playerStatus(state.showdown),
    hasRegistrationEnded: state.otherStates.hasRegistrationEnded,
    hasUserRegistered: state.otherStates.hasUserRegistered,
    registrationEndTime: showdownReader.registrationEndTime(state.showdown),
    hasJoined: _get(
      state.showdown,
      ['currentUserParticipation', 'currentRound', 'hasJoined'],
      false,
    ),
  }));

  const registrationEndTimeInMs = new Date(registrationEndTime).getTime();

  const startTimeInMs = new Date(startTime).getTime();
  const endTimeInMs = new Date(endTime).getTime();

  if (hasEnded) {
    return (
      <TimerComponent
        title="Showdown Ended"
        // description="Try again next time"
      />
    );
  }

  if (hasRegistrationEnded && !hasUserRegistered) {
    return <TimerComponent title="Registration Ended" />;
  }

  if (!hasUserRegistered && !hasRegistrationEnded) {
    return (
      <TimerComponent
        title="Registration Ends in"
        timerEndsAt={registrationEndTimeInMs}
      />
    );
  }

  if (!isLive) {
    return (
      <TimerComponent title="Showdown Starts in" timerEndsAt={startTimeInMs} />
    );
  }

  if (isBreak) {
    return <TimerComponent title="Break Ends in" timerEndsAt={breakEndsAt} />;
  }

  if (currentRound < totalRounds) {
    if (!canUserJoin && playerStatus === SHOWDOWN_PLAYER_STATUS.PENDING_JOIN && !hasJoined) {
      return (
        <TimerComponent title="Missed The Round" timerEndsAt={breakEndsAt} />
      );
    }
    return (
      <TimerComponent
        title={`Round ${currentRound} Ends in`}
        timerEndsAt={roundEndsAt}
      />
    );
  }

  return <TimerComponent title="Showdown Ends in" timerEndsAt={endTimeInMs} />;
});

export default React.memo(ShowdownRightPaneTimer);
