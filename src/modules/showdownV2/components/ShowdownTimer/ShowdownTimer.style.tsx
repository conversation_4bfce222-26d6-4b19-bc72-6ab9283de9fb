import dark from 'core/constants/themes/dark';
import useMediaQuery from '@/src/core/hooks/useMediaQuery';
import { useMemo } from 'react';

const useStyles = () => {
  const { isMobile } = useMediaQuery();

  const styles = useMemo(
    () => ({
      infoItem: {
        display: 'flex',
        gap: 10,
        flexDirection: 'row',
        alignItems: 'center',
      },
      iconContainer: {
        backgroundColor: isMobile ? dark.colors.primary : dark.colors.tertiary,
        padding: isMobile ? 6 : 10,
        borderRadius: isMobile ? 8 : 10,
        width: isMobile ? 34 : 40,
        height: isMobile ? 34 : 40,
        justifyContent: 'center',
        alignItems: 'center',
      },
      iconStyle: {
        height: isMobile ? 20 : 24,
        width: isMobile ? 20 : 24,
      },
      container: {
        flex: 1,
        backgroundColor: 'transparent',
        padding: 20,
      },

      infoContainer: {
        flexDirection: 'column',
        alignItems: 'flex-start',
        gap: 15,
        padding: 20,
        borderWidth: 2,
        borderColor: dark.colors.tertiary,
        borderRadius: 10,
      },
      info: {
        gap: 4,
        display: 'flex',
        alignContent: 'center',
        justifyContent: 'center',
        flexDirection: 'column',
      },
      infoText: {
        fontFamily: 'Montserrat-500',
        lineHeight: 12,
        fontSize: 12,
        color: dark.colors.textDark,
        // marginTop: 5,
      },
      icon: {
        fontSize: 24,
        color: '#FFF',
      },
      infoNumber: {
        lineHeight: 14,
        color: '#FFF',
        fontSize: 13.5,
        marginTop: 5,
      },
      infoNumberMobile: {
        lineHeight: 12,
        color: '#FFF',
        fontSize: 12,
        marginTop: 5,
      },
    }),
    [isMobile],
  );

  return styles;
};

export default useStyles;
