import React, { useCallback, useMemo, useState } from 'react';
import useShowdownStore from '@/src/store/useShowdownStore';
import ContestTabBarShimmer from '@/src/modules/contest/shimmers/ContestDetailTabBarShimmer';
import Analytics from '@/src/core/analytics';
import { PAGE_NAMES, PAGE_NAME_KEY } from '@/src/core/constants/pageNames';
import { Dimensions, Text, View } from 'react-native';
import { TabView, TabBar } from 'react-native-tab-view';
import dark from '@/src/core/constants/themes/dark';
import DetailRoute from './DetailRoute';
import Fixtures from '../Fixtures';
import { TAB_KEYS, TAB_KEY_VS_EVENT } from '../../constants/showdownDetails';
import styles from './SHowdownExpandedTabBar.style';
import ShowdownLeaderboard from '../ShowdownLeaderboard';

const initialLayout = { width: Dimensions.get('window').width, elevation: 0 };

const ShowdownTabBar = React.memo(() => {
  //   const { isLive, hasUserRegistered, hasEnded, showdown, currentRound } = state;
  const [index, setIndex] = useState(0);

  const { hasUserRegistered, hasEnded, isLive, getShowdownPropertiesToTrack } =
    useShowdownStore((state) => ({
      isLive: state.otherStates.isLive,
      hasUserRegistered: state.otherStates.hasUserRegistered,
      hasEnded: state.otherStates.hasEnded,
      getShowdownPropertiesToTrack: state.getShowdownPropertiesToTrack,
    }));
  const routes = useMemo(() => {
    const availableRoutes = [];
    if (!hasUserRegistered) {
      availableRoutes.push({ key: TAB_KEYS.DETAILS, title: 'Details' });
      return availableRoutes;
    }
    if (!hasEnded) {
      availableRoutes.push({
        key: TAB_KEYS.FIXTURES,
        title: 'Fixtures',
      });
    }
    if (isLive || hasEnded) {
      availableRoutes.push({
        key: TAB_KEYS.LEADERBOARD,
        title: 'Leaderboard',
      });
    }
    availableRoutes.push({ key: TAB_KEYS.DETAILS, title: 'Details' });
    return availableRoutes;
  }, [hasUserRegistered, hasEnded, isLive]);

  const onIndexChange = useCallback(
    (updatedIndex: number) => {
      setIndex(updatedIndex);
      const route = routes[updatedIndex];
      if (TAB_KEY_VS_EVENT?.[route?.key]) {
        Analytics.track(TAB_KEY_VS_EVENT[route.key], {
          ...getShowdownPropertiesToTrack(),
          [PAGE_NAME_KEY]: PAGE_NAMES.SHOWDOWN_DETAILS,
        });
      }
    },
    [setIndex, routes, getShowdownPropertiesToTrack],
  );

  const renderScene = useCallback(({ route }: { route: any }) => {
    switch (route.key) {
      case TAB_KEYS.DETAILS:
        return <DetailRoute />;
      case TAB_KEYS.FIXTURES:
        return <Fixtures />;
      case TAB_KEYS.LEADERBOARD:
        return <ShowdownLeaderboard />;
      default:
        return <DetailRoute />;
    }
  }, []);

  const renderTabBar = useCallback(
    (props: any) =>
      routes.length > 1 ? (
        <View style={styles.tabBarContainer}>
          <TabBar
            {...props}
            indicatorStyle={styles.indicator}
            style={styles.tabBar}
            tabStyle={styles.tabStyle}
            labelStyle={styles.label}
            activeColor={dark.colors.secondary}
            inactiveColor={dark.colors.textDark}
            renderLabel={({ route, color }) => (
              <Text style={{ ...styles.label, color }}>{route.title}</Text>
            )}
          />
          <View style={styles.fullWidthLine} />
        </View>
      ) : (
        <View style={{ height: 20 }} />
      ),
    [routes],
  );

  return (
    <TabView
      navigationState={{ index, routes }}
      renderScene={renderScene}
      onIndexChange={onIndexChange}
      initialLayout={initialLayout}
      renderTabBar={renderTabBar}
    />
  );
});

const ShowDownTabBarContainer = () => {
  const { loading, isFixtureAvailable } = useShowdownStore((state) => ({
    loading: state.fixturesLoading,
    isFixtureAvailable: state.isFixtureAvailable,
  }));

  if (loading && !isFixtureAvailable) {
    return <ContestTabBarShimmer />;
  }

  return <ShowdownTabBar />;
};

export default React.memo(ShowDownTabBarContainer);
