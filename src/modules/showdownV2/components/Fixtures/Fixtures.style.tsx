import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    // marginTop: 20,
    marginTop: 12,
    width: '100%',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  currentFixtureContainer: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingTop: 20,
    paddingBottom: 15,
    marginBottom: 16,
    borderRadius: 12,
  },
  currentFictureContainer1: {
    gap: 6,
    alignItems: 'flex-start',
    justifyContent: 'center',
    flex: 1,
  },
  currentFictureContainer2: {
    gap: 6,
    alignItems: 'flex-end',
    justifyContent: 'center',
    flex: 1,
  },
  fixturesListStyle: {
    width: '100%',
  },
});

export default styles;
