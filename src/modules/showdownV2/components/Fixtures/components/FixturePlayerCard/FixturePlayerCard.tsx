import React from 'react';
import { Image, Text, View } from 'react-native';

import _truncate from 'lodash/truncate';
import showdownParticipantReader from '@/src/modules/showdown/readers/showdownParticipantReader';
import styles from './FixturePlayerCard.style';

const FixturePlayerCard = ({ participant }: { participant: any }) => {
  const userName = _truncate(showdownParticipantReader.username(participant), {
    length: 10,
    omission: '...',
  });

  return (
    <View style={styles.container}>
      <Image
        style={styles.profileImage}
        source={{
          uri: showdownParticipantReader.userImageUri(participant),
        }}
      />
      <Text style={styles.userName}>{userName}</Text>
      <Text style={styles.userRating}>
        {showdownParticipantReader.rating(participant)}
      </Text>
    </View>
  );
};

export default React.memo(FixturePlayerCard);
