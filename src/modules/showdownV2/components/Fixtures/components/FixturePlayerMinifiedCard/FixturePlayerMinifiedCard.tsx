import React from 'react';
import { Image, Text, View } from 'react-native';

import showdownParticipantReader from 'modules/showdownV2/readers/showdownParticipantReader';
import styles from './FixturePlayerMinifiedCard.styles';

const FixturePlayerMinifiedCard = ({ participant }: { participant: any }) => (
  <View style={styles.container}>
    <Image
      style={styles.userImage}
      source={{
        uri: showdownParticipantReader.userImageUri(participant),
      }}
    />
    <View>
      <Text style={styles.userName}>
        {showdownParticipantReader.username(participant)}
      </Text>
      <Text style={styles.userRating}>
        {showdownParticipantReader.rating(participant)}
      </Text>
    </View>
  </View>
);

export default React.memo(FixturePlayerMinifiedCard);
