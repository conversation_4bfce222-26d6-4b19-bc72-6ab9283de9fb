import { Text, View } from 'react-native';
import React from 'react';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import showdownReader from 'modules/showdownV2/readers/showdownReader';
import styles from './FixturePlayersRow.styles';

import FixturePlayerMinifiedCard from '../FixturePlayerMinifiedCard';

const FixturePlayersRow = (props: { item: any }) => {
  const { item } = props;
  const showdownUsers = showdownReader.users(item);
  const firstParticipant = _get(
    showdownUsers,
    [0, 'showdownParticipant'],
    EMPTY_OBJECT,
  );
  const secondParticipant = _get(
    showdownUsers,
    [1, 'showdownParticipant'],
    EMPTY_OBJECT,
  );

  const participantOneScore = _get(
    showdownUsers,
    [0, 'currentRound', 'wins'],
    0,
  );
  const participantTwoScore = _get(
    showdownUsers,
    [1, 'currentRound', 'wins'],
    0,
  );

  const renderPlayerScore = () => (
    <View style={styles.scoreContainer}>
      <Text
        style={styles.score}
      >{`${participantOneScore} - ${participantTwoScore}`}</Text>
    </View>
  );

  if (_isEmpty(firstParticipant) || _isEmpty(secondParticipant)) {
    return null;
  }

  return (
    <View style={styles.container}>
      <FixturePlayerMinifiedCard participant={firstParticipant} />
      {renderPlayerScore()}
      <View style={{ flex: 1, alignItems: 'flex-end' }}>
        <FixturePlayerMinifiedCard participant={secondParticipant} />
      </View>
    </View>
  );
};

export default React.memo(FixturePlayersRow);
