/* eslint-disable import/no-unused-modules */
import React, { useMemo } from 'react';
import { Text, View } from 'react-native';
import _get from 'lodash/get';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { SHOWDOWN_PLAYER_STATUS } from 'modules/showdownV2/constants/showdownPlayerStatus';
import dark from 'core/constants/themes/dark';
import showdownReader from 'modules/showdownV2/readers/showdownReader';
import useShowdownStore from 'store/useShowdownStore';
import useCountDownTimer from 'modules/showdownV2/hooks/useCountDownTimer';
import _isNaN from 'lodash/isNaN';
import FixturePlayerCard from '../FixturePlayerCard';
import styles from './FixtureHeader.style';

const Timer = React.memo(({ timer }: { timer: number }) => {
  const { formattedTime } = useCountDownTimer({
    targetTimeStamp: timer,
  });
  return <Text>{formattedTime}</Text>;
});

export const FixtureHeader = () => {
  const { isMobile } = useMediaQuery();

  const {
    startTime,
    totalRounds,
    fixtures,
    hasEnded,
    playerStatus,
    isLive,
    breakEndsAt,
    isRoundActive,
    currentRound,
    currentRoundInfo,
    numOfGames,
  } = useShowdownStore((state) => ({
    startTime: showdownReader.startTime(state.showdown),
    totalRounds: showdownReader.roundsCount(state.showdown),
    fixtures: state.fixtures,
    currentRound: state.otherStates.currentRound,
    hasEnded: state.otherStates.hasEnded,
    playerStatus: showdownReader.playerStatus(state.showdown),
    isLive: state.otherStates.isLive,
    breakEndsAt: state.otherStates.breakEndsAt,
    isRoundActive: state.otherStates.isRoundActive,
    currentRoundInfo: showdownReader.currentRoundInfo(state.showdown),
    numOfGames: showdownReader.totalGamesInRound(state.showdown),
  }));

  const currentGame = useMemo(() => {
    if (_isNaN(numOfGames) || _isNaN(currentRoundInfo?.totalGamesPlayed))
      return 1;
    return Math.min(
      (currentRoundInfo?.totalGamesPlayed as number) + 1,
      numOfGames,
    );
  }, [numOfGames, currentRoundInfo]);

  const users = useMemo(() => _get(fixtures, 'users', []), [fixtures]);

  const status = useMemo(() => {
    const userData = _get(fixtures, 'users', []);
    const currentUserScore = _get(userData, [0, 'currentRound', 'wins'], 0);
    const opponentScore = _get(userData, [1, 'currentRound', 'wins'], 0);
    const data = { currentUserScore, opponentScore };
    if (!userData) {
      return data;
    }
    if (hasEnded) {
      return { ...data, title: 'ENDED', color: dark.colors.error };
    }
    if (playerStatus === SHOWDOWN_PLAYER_STATUS.ROUND_COMPLETED) {
      if (currentUserScore > opponentScore) {
        return { ...data, title: 'WON', color: dark.colors.success };
      }
      if (currentUserScore < opponentScore) {
        return { ...data, title: 'LOST', color: dark.colors.error };
      }
      if (currentUserScore === opponentScore) {
        return { ...data, title: 'TIE', color: dark.colors.textLight };
      }
    }
    return { ...data, title: 'LIVE', color: dark.colors.textLight };
  }, [fixtures, hasEnded, playerStatus]);

  const timeToShow = useMemo(() => {
    if (isLive && !isRoundActive && !hasEnded) {
      return breakEndsAt;
    }
    if (!isLive && !hasEnded) {
      return new Date(startTime).getTime();
    }
    return 0;
  }, [breakEndsAt, startTime, hasEnded, isRoundActive, isLive]);

  return (
    <View
      style={[
        styles.container,
        {
          paddingHorizontal: 20,
        },
        isMobile && {
          backgroundColor: dark.colors.gradientBackground,
        },
      ]}
    >
      <View style={styles.player1CardContainer}>
        <FixturePlayerCard
          participant={_get(users, [0, 'showdownParticipant'], EMPTY_OBJECT)}
        />
      </View>
      <View style={styles.infoContainer}>
        <Text style={styles.currentRoundTitle}>
          ROUND {_get(fixtures, 'round', 0)}
        </Text>
        <Text style={styles.gamesPlayedInfo}>
          Game {currentGame} of {numOfGames}
        </Text>
        <Text style={styles.winsInfo}>
          {`${_get(users, [0, 'currentRound', 'wins'], 0)} - ${_get(users, [1, 'currentRound', 'wins'], 0)}`}
        </Text>

        {(!isLive && currentRound === 1) ||
        (!isRoundActive &&
          playerStatus === SHOWDOWN_PLAYER_STATUS.PENDING_JOIN &&
          currentRound !== totalRounds) ? (
          <View style={styles.timer}>
            <Text style={styles.timerTextStyle}>
              Starts in <Timer timer={timeToShow} />
            </Text>
          </View>
        ) : (
          <View style={{ marginTop: 14 }}>
            <Text
              style={[
                styles.liveText,
                {
                  color: _get(status, 'color', dark.colors.textLight),
                },
              ]}
            >
              {_get(status, 'title', 'LIVE!')}
            </Text>
          </View>
        )}
      </View>
      <View style={styles.player2CardContainer}>
        <FixturePlayerCard
          participant={_get(users, [1, 'showdownParticipant'], EMPTY_OBJECT)}
        />
      </View>
    </View>
  );
};

export default React.memo(FixtureHeader);
