import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingTop: 20,
    paddingBottom: 15,
    marginBottom: 16,
    borderRadius: 12,
  },
  player1CardContainer: {
    gap: 6,
    alignItems: 'flex-start',
    justifyContent: 'center',
    flex: 1,
  },
  player2CardContainer: {
    gap: 6,
    alignItems: 'flex-end',
    justifyContent: 'center',
    flex: 1,
  },
  infoContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 4,
    paddingBottom: 3,
    flex: 2,
  },
  currentRoundTitle: {
    color: dark.colors.textLight,
    fontSize: 10,
    lineHeight: 12.9,
    fontWeight: '700',
  },
  winsInfo: {
    color: dark.colors.textLight,
    fontSize: 14,
    lineHeight: 17,
    marginTop: 8,
  },
  gamesPlayedInfo: {
    color: dark.colors.textDark,
    fontSize: 10,
    lineHeight: 12.9,
    fontWeight: '500',
    marginTop: 4,
  },
  timer: {
    borderWidth: 1,
    borderRadius: 12,
    height: 20,
    marginTop: 20,
    borderColor: dark.colors.tertiary,
    backgroundColor: dark.colors.primary,
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  timerTextStyle: {
    color: '#fff',
    fontSize: 10,
    lineHeight: 12.9,
    // marginTop: 8,
  },
  liveText: {
    color: '#fff',
    letterSpacing: 1,
    fontSize: 14,
    fontWeight: '700',
  },
});

export default styles;
