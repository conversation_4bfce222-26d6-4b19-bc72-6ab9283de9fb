import Dark from '@/src/core/constants/themes/dark';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  gradientBox: {
    width: 120,
    height: 120,
    borderRadius: 120,
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  icon: {
    width: 96,
    height: 96,
  },
  infoContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  roundInfoText: {
    textAlign: 'center',
    fontSize: 16,
    fontFamily: 'Montserrat-700',
    lineHeight: 19,
    letterSpacing: 2,
    color: Dark.colors.textDark,
    marginBottom: 10,
  },
  descText: {
    textAlign: 'center',
    fontSize: 16,
    fontFamily: 'Montserrat-500',
    lineHeight: 24,
    color: Dark.colors.textLight,
    // marginBottom: 5,
  },
  timerText: {
    textAlign: 'center',
    fontSize: 16,
    fontFamily: 'Montserrat-700',
    lineHeight: 24,
    letterSpacing: 2,
    color: Dark.colors.textLight,
  },
});

export default styles;
