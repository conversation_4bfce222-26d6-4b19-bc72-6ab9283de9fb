import React, { useMemo } from 'react';
import { View } from 'react-native';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';

import Loading from '@/src/components/atoms/Loading';
import useShowdownStore from 'store/useShowdownStore';
import _isNaN from 'lodash/isNaN';
import FixtureCantPlayInfo from './components/FixtureCantPlayInfo';

import styles from './Fixtures.style';
import useUserCantPlay from '../../hooks/useUserCantPlay';
import FixtureHeader from './components/FixtureHeader';
import FixtureCreationPending from './components/FixtureCreationPending';
import showdownReader from '../../readers/showdownReader';
import fixtureReader from '../../readers/fixtureReader';
import { getshouldShowFixtureCreationPending } from './helpers';

const Fixtures = () => {
  const {
    startTime,
    gapBwRounds,
    isRoundActive,
    currentRound,
    isLive,
    isLoading,
    fixtureCurrentRound,
    hasEnded,
    roundEndsAt,
    currentRoundInfo,
    playerStatus,
    hasJoined,
  } = useShowdownStore((state) => ({
    startTime: state.showdown ? showdownReader.startTime(state.showdown) : '',
    gapBwRounds: showdownReader.gapBwRounds(state.showdown),
    isRoundActive: state.otherStates.isRoundActive,
    currentRound: state.otherStates.currentRound,
    isLive: state.otherStates.isLive,
    isLoading: state.fixturesLoading,
    fixtureCurrentRound: fixtureReader.currentRound(state.fixtures) ?? 0,
    hasEnded: state.otherStates.hasEnded,
    roundEndsAt: state.otherStates.roundEndsAt,
    currentRoundInfo: showdownReader.currentRoundInfo(state.showdown),
    playerStatus: showdownReader.playerStatus(state.showdown),
    hasJoined: _get(
      state.showdown,
      ['currentUserParticipation', 'currentRound', 'hasJoined'],
      false,
    ),
  }));

  const userCantPlayConfig = useUserCantPlay();

  const shouldShowFixtureCreationPending = useMemo(
    () =>
      getshouldShowFixtureCreationPending(
        fixtureCurrentRound,
        currentRound,
        isRoundActive,
        hasEnded,
        currentRoundInfo,
        playerStatus,
      ),
    [
      fixtureCurrentRound,
      currentRound,
      isRoundActive,
      hasEnded,
      currentRoundInfo,
      playerStatus,
    ],
  );

  const timer = useMemo(() => {
    if (!isLive) {
      const start = new Date(startTime);
      if (_isNaN(start.getTime())) {
        return 0;
      }
      return Math.floor(start.getTime() - 30 * 1000);
    }
    if (roundEndsAt) {
      const roundEnd = new Date(roundEndsAt);
      if (_isNaN(roundEnd.getTime())) {
        return 0;
      }
      return Math.floor(roundEnd.getTime() + (gapBwRounds / 2) * 1000);
    }
    return 0;
  }, [startTime, roundEndsAt, gapBwRounds, isLive]);

  if (isLoading) {
    return <Loading />;
  }

  if (shouldShowFixtureCreationPending) {
    return (
      <FixtureCreationPending currentRound={currentRound + 1} timer={timer} />
    );
  }

  return (
    <View style={styles.container}>
      {!_isEmpty(userCantPlayConfig) ? (
        <FixtureCantPlayInfo
          userCantPlayConfig={userCantPlayConfig}
          currentRound={currentRound}
          isRoundActive={isRoundActive}
        />
      ) : (
        <FixtureHeader />
      )}
    </View>
  );
};

export default React.memo(Fixtures);
