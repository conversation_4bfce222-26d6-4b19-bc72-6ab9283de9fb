/* eslint-disable import/prefer-default-export */
import { SHOWDOWN_PLAYER_STATUS } from '../../constants/showdownPlayerStatus';

export const getshouldShowFixtureCreationPending = (
  fixtureCurrentRound: number,
  currentRound: number,
  isRoundActive: boolean,
  hasEnded: boolean,
  currentRoundInfo: any,
  playerStatus: string,
) => {
  if (hasEnded) {
    return false;
  }
  if (
    ((currentRoundInfo?.round === currentRound + 1 && !isRoundActive) ||
      (currentRoundInfo?.round === currentRound && isRoundActive)) &&
    playerStatus === SHOWDOWN_PLAYER_STATUS.BYE
  ) {
    return false;
  }
  if (fixtureCurrentRound === 0) {
    return true;
  }

  if (fixtureCurrentRound !== currentRound + 1 && !isRoundActive) {
    return true;
  }
  if (fixtureCurrentRound !== currentRound && isRoundActive) {
    return true;
  }

  return false;
};
