import React from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import Ionicons from '@expo/vector-icons/Ionicons';
import HtmlRenderer from 'atoms/HtmlRenderer';
import styles from './ExpandableContainer.style';

const ExpandableContainer = ({
  title,
  content,
  isActive,
  changeActiveAccordian,
}: {
  title: string;
  content: string;
  isActive: boolean;
  changeActiveAccordian: (_title: string) => void;
}) => (
  <ScrollView style={styles.container}>
    <TouchableOpacity
      onPress={() => changeActiveAccordian(title)}
      style={styles.header}
    >
      <Text style={styles.headerText}>{title}</Text>
      <Ionicons
        name={isActive ? 'chevron-up-outline' : 'chevron-down-outline'}
        size={20}
        color="white"
      />
    </TouchableOpacity>
    {isActive && (
      <View style={styles.content}>
        <HtmlRenderer html={content} />
      </View>
    )}
  </ScrollView>
);

export default ExpandableContainer;
