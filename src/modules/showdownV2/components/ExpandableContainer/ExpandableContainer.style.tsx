import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  container: {
    marginBottom: 10,
    borderWidth: 2,
    borderRadius: 5,
    backgroundColor: dark.colors.primary,
    borderColor: dark.colors.tertiary,
  },
  header: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: dark.colors.primary,
    paddingHorizontal: 16,
    paddingVertical: 10,
  },
  headerText: {
    fontFamily: 'Montserrat-500',
    fontSize: 15,
    color: 'white',
  },
  content: {
    padding: 10,
    borderTopColor: dark.colors.tertiary,
    borderTopWidth: 2,
    backgroundColor: '#242424',
  },
});

export default styles;
