import React, { useCallback, useState, useMemo, useEffect } from 'react';
import { View, Dimensions, Text } from 'react-native';
import { TabBar, TabView } from 'react-native-tab-view';
import dark from 'core/constants/themes/dark';
import Analytics from 'core/analytics';
import { PAGE_NAME_KEY, PAGE_NAMES } from 'core/constants/pageNames';
import useShowdownStore from '@/src/store/useShowdownStore';
import styles from './ShowdownCompactTabBar.style';

import { TAB_KEY_VS_EVENT, TAB_KEYS } from '../../constants/showdownDetails';
import ShowdownLeaderboard from '../ShowdownLeaderboard';
import Fixtures from '../Fixtures';
import CompactDetailsRoute from './components/CompactDetailsRoute';

const initialLayout = { width: Dimensions.get('window').width };

const CompactShowdownDetailsTabBarView = React.memo(() => {
  const [index, setIndex] = useState(0);

  const { getShowdownPropertiesToTrack, hasUserRegistered, hasEnded, isLive } =
    useShowdownStore((state) => ({
      getShowdownPropertiesToTrack: state.getShowdownPropertiesToTrack,
      hasUserRegistered: state.otherStates.hasUserRegistered,
      hasEnded: state.otherStates.hasEnded,
      isLive: state.otherStates.isLive,
    }));

  const routes = useMemo(() => {
    const availableRoutes = [];
    if (!hasUserRegistered) {
      availableRoutes.push({ key: TAB_KEYS.DETAILS, title: 'Details' });
      return availableRoutes;
    }
    if (!hasEnded) {
      availableRoutes.push({
        key: TAB_KEYS.FIXTURES,
        title: 'Fixtures',
      });
    }
    if (isLive || hasEnded) {
      availableRoutes.push({
        key: TAB_KEYS.LEADERBOARD,
        title: 'Leaderboard',
      });
    }
    availableRoutes.push({ key: TAB_KEYS.DETAILS, title: 'Details' });
    return availableRoutes;
  }, [isLive, hasUserRegistered, hasEnded]);

  const onIndexChange = useCallback(
    (updatedIndex: number) => {
      setIndex(updatedIndex);
      const route = routes[updatedIndex];
      if (TAB_KEY_VS_EVENT?.[route?.key]) {
        const properties = getShowdownPropertiesToTrack() ?? EMPTY_OBJECT;
        Analytics.track(TAB_KEY_VS_EVENT[route.key], {
          ...properties,
          [PAGE_NAME_KEY]: PAGE_NAMES.SHOWDOWN_DETAILS,
        });
      }
    },
    [routes, setIndex, getShowdownPropertiesToTrack],
  );

  const renderScene = useCallback(({ route }: { route: any }) => {
    switch (route.key) {
      case TAB_KEYS.FIXTURES:
        return <Fixtures />;
      case TAB_KEYS.LEADERBOARD:
        return <ShowdownLeaderboard />;
      case TAB_KEYS.DETAILS:
        return <CompactDetailsRoute />;
      default:
        return <CompactDetailsRoute />;
    }
  }, []);

  const renderTabBar = useCallback(
    (props: any) =>
      routes.length > 1 ? (
        <View style={styles.tabBarContainer}>
          <TabBar
            {...props}
            indicatorStyle={styles.indicator}
            style={styles.tabBar}
            tabStyle={styles.tabStyle}
            labelStyle={styles.label}
            activeColor={dark.colors.secondary}
            inactiveColor={dark.colors.textDark}
            // scrollEnabled={false}
            renderLabel={({ route, color }) => (
              <Text style={{ ...styles.label, color }}>{route.title}</Text>
            )}
          />
          {/* <View style={styles.fullWidthLine} /> */}
        </View>
      ) : null,
    [routes],
  );

  useEffect(() => {
    if (index >= routes.length) {
      setIndex(0);
    }
  }, [routes, index]);

  return (
    <TabView
      navigationState={{ index, routes }}
      renderScene={renderScene}
      onIndexChange={onIndexChange}
      swipeEnabled={false}
      initialLayout={initialLayout}
      renderTabBar={renderTabBar}
    />
  );
});

export default React.memo(CompactShowdownDetailsTabBarView);
