import font from '@/src/core/constants/fonts';
import dark from 'core/constants/themes/dark';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    paddingVertical: 10,
    paddingBottom: 80,
    width: '100%',
  },
  detailBoxContainer: {
    width: '100%',
    paddingHorizontal: 16,
    marginBottom: 12,
  },
  detailBox: {
    borderColor: '#3A3A3A',
    flexDirection: 'row',
    // gap: 13,
    borderWidth: 1,
    marginBottom: 10,
    borderRadius: 8,
    padding: 12,
    width: '100%',
    paddingHorizontal: 16,
    paddingVertical: 12,
    justifyContent: 'center',
    alignItems: 'center',
    // marginHorizontal: 16
  },
  registrationCOuntContainer: {
    height: '100%',
    paddingVertical: 3,
    gap: 6,
  },
  registrationCountText: {
    color: dark.colors.textLight,
    fontSize: 12,
    lineHeight: 12,
    fontFamily: font.MONTSERRAT_SEMI_BOLD,
  },
  registrationTitleText: {
    color: dark.colors.textDark,
    fontSize: 11,
    lineHeight: 12.5,
    fontFamily: font.MONTSERRAT_REGULAR,
  },
  iconContainer: {
    width: 36,
    height: '100%',
    borderRadius: 8,
    backgroundColor: dark.colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  infoContainer: {
    flex: 1,
    width: '48%',
    height: 36,
    gap: 8,
    flexDirection: 'row',
  },
});

export default styles;
