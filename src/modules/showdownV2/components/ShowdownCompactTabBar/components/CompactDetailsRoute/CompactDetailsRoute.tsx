import React, { useState } from 'react';
import { ScrollView, Text, View } from 'react-native';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';
import { ACCORDION } from 'modules/showdownV2/constants';
import useShowdownStore from 'store/useShowdownStore';
import showdownReader from 'modules/showdownV2/readers/showdownReader';
import ShowdownTimer from 'modules/showdownV2/components/ShowdownTimer';
import CompactExpandableSection from '../CompactExpandableSection';
import styles from './CompactDetailsRoute.style';

const ShowdownDetailRoute = () => {
  const [activeAccordian, setActiveAccordian] = useState('');

  const { showdownDetails, registrationCount } = useShowdownStore((state) => ({
    showdownDetails: showdownReader.details(state.showdown),
    registrationCount: showdownReader.registrationCount(state.showdown) ?? 0,
  }));

  const changeActiveAccordian = (accordian: any) => {
    setActiveAccordian((prev) => {
      if (prev === accordian) {
        return '';
      }
      return accordian;
    });
  };
  return (
    <ScrollView contentContainerStyle={styles.container}>
      <View style={styles.detailBoxContainer}>
        <View style={styles.detailBox}>
          <View style={styles.infoContainer}>
            <View style={styles.iconContainer}>
              <MaterialCommunityIcons
                name="account-group-outline"
                size={20}
                color="#D9D9D9"
              />
            </View>
            <View style={styles.registrationCOuntContainer}>
              <Text style={styles.registrationTitleText}>Registered</Text>
              <Text style={styles.registrationCountText}>
                {registrationCount}
              </Text>
            </View>
          </View>
          <View style={{ flex: 1, width: '48%' }}>
            <View>
              <ShowdownTimer />
            </View>
          </View>
        </View>
      </View>

      {!_isEmpty(ACCORDION)
        ? ACCORDION.map((accordian: any) => {
            const accordianContent = _get(showdownDetails, accordian.key, '');
            return (
              <CompactExpandableSection
                key={`SHOWDOWN_DETAIL_${accordian.title}`}
                title={accordian?.title}
                expanded={activeAccordian === accordian?.title}
                content={accordianContent}
                onToggle={() => changeActiveAccordian(accordian?.title)}
              />
            );
          })
        : null}
    </ScrollView>
  );
};

export default React.memo(ShowdownDetailRoute);
