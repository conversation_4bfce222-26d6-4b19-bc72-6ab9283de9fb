import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import Ionicons from '@expo/vector-icons/Ionicons';
import HtmlRenderer from 'atoms/HtmlRenderer';
import dark from 'core/constants/themes/dark';
import styles from './CompactExpandableSection.style';

const ExpandableSection = ({ title, expanded, onToggle, content }: any) => (
  <View style={styles.expandableContainer}>
    <TouchableOpacity style={styles.expandableHeader} onPress={onToggle}>
      <Text style={styles.expandableText}>{title}</Text>
      <Ionicons
        name={expanded ? 'chevron-up-outline' : 'chevron-down-outline'}
        size={15}
        color={dark.colors.secondary}
      />
    </TouchableOpacity>
    {expanded && (
      <View style={styles.expandedContent}>
        <HtmlRenderer html={content} />
      </View>
    )}
  </View>
);

export default React.memo(ExpandableSection);
