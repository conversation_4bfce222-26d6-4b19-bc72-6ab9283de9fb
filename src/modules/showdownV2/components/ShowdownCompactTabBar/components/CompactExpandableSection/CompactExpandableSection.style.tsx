import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';
import font from '@/src/core/constants/fonts';

const styles = StyleSheet.create({
  expandableContainer: {
    borderRadius: 10,
    borderWidth: 1,
    borderColor: dark.colors.tertiary,
    marginBottom: 12,
    marginHorizontal: 16,
  },
  expandableHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
    alignItems: 'center',
    height: 44,
    backgroundColor: dark.colors.primary,
    // paddingVertical: 10,
  },
  expandableText: {
    color: dark.colors.textDark,
    fontSize: 12,
    lineHeight: 24,
    fontFamily: font.MONTSERRAT_MEDIUM,
    letterSpacing: -0.15,
  },
  expandedContent: {
    paddingHorizontal: 15,
    paddingVertical: 10,
    // backgroundColor: '#2A2A2A',
  },
});

export default styles;
