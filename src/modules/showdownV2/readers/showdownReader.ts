import _property from 'lodash/property';
import _get from 'lodash/get';
import { SHOWDOWN_PLAYER_STATUS } from '../constants/showdownPlayerStatus';

const showdownReader = {
  id: _property('_id'),
  name: _property('name'),
  users: _property('users'),
  status: _property('status'),
  details: _property('details'),
  roundsCount: (showdown: any) => _get(showdown, ['rounds'], 0),
  startTime: (showdown: any) => _get(showdown, ['startTime'], ''),
  gapBwRounds: _property('gapBwRounds'),
  roundTime: _property('roundTime'),
  duration: _property('duration'),
  endTime: (showdown: any) => _get(showdown, ['endTime'], ''),
  registrationEndTime: (showdown: any) =>
    _get(showdown, ['registrationEndTime'], ''),
  registrationCount: _property('registrationCount'),
  currentRoundNumber: _property('currentRound'),
  currentUserParticipation: _property('currentUserParticipation'),
  totalGamesInRound: (showdown: any) =>
    _get(showdown, ['roundConfig', 'numOfGames'], 0),
  maxWaitTime: _property(['roundConfig', 'maxWaitTime']),
  playerStatus: (showdown: any) =>
    _get(
      showdownReader.currentRoundInfo(showdown),
      'playerStatus',
      SHOWDOWN_PLAYER_STATUS.PENDING_JOIN,
    ),
  currentRoundInfo: (showdown: any) =>
    _get(showdown, ['currentUserParticipation', 'currentRound'], EMPTY_OBJECT),
};

export default showdownReader;
