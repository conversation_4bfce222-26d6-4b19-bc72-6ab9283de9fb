import { View } from 'react-native';
import React from 'react';
import { ExpandedShowdownDetailsStyles } from './styles';
import ShowdownDetailsContainer from '../../components/ShowdownDetailsContainer';
import ShowdownRightPane from '../../components/ShowdownRightPane';
import ShowdownExpandedTabBar from '../../components/ShowdownExpandedTabBar';

const ExpandedShowdownDetails = () => (
  <View style={ExpandedShowdownDetailsStyles.container}>
    <View style={ExpandedShowdownDetailsStyles.contentContainer}>
      <ShowdownDetailsContainer />
      <View style={{ flex: 1 }}>
        <ShowdownExpandedTabBar />
      </View>
    </View>
    <View style={ExpandedShowdownDetailsStyles.rightPaneContainer}>
      <ShowdownRightPane />
    </View>
  </View>
);

export default React.memo(ExpandedShowdownDetails);
