import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { View } from 'react-native';
import _isEmpty from 'lodash/isEmpty';
import _compact from 'lodash/compact';
import { BANNER_TYPES } from 'core/constants/bannerTypes';
import useMediaQuery from 'core/hooks/useMediaQuery';
import userReader from 'core/readers/userReader';
import { getStorageState } from 'core/hooks/useStorageState';
import _isNaN from 'lodash/isNaN';
import _get from 'lodash/get';

import useGetFeaturedShowdown from '../../../showdown/hooks/query/useGetFeaturedShowdown';
import HomeBannerCarouselShimmer from './Shimmer/HomeBannerCarouselShimmer';
import HomeBannerCarousel from './HomeBannerCarousel';
import useFetchFeaturedContests from '../../../contest/hooks/useFetchFeaturedContests';
import { useSession } from '../../../auth/containers/AuthProvider';

const HomeBannerCarouselContainer = () => {
  const { user = EMPTY_OBJECT } = useSession();
  const { featuredContests, loading } = useFetchFeaturedContests();
  const { featuredShowdowns, loading: isFeaturedShowdownLoading } =
    useGetFeaturedShowdown();
  const [containerWidth, setContainerWidth] = useState(0);
  const { isMobile: isCompactMode } = useMediaQuery();

  const [isPledgeAlreadyTaken, setIsAlreadyPledgeTaken] = useState(false);
  const toShowContestBanner = !_isEmpty(featuredContests);

  const featuredShowdown = useMemo(() => {
    if (featuredShowdowns.length === 0) {
      return EMPTY_OBJECT;
    }
    const currentShowdown = _get(featuredShowdowns, 0);
    return currentShowdown;
  }, [featuredShowdowns]);
  const toShowShowdownBanner = !_isEmpty(featuredShowdown);

  const hasFixedRating = userReader.hasFixedRating(user);

  useEffect(() => {
    getStorageState('commitmentTime')
      .then((commitmentTime) => {
        if (_isNaN(commitmentTime) || commitmentTime <= 0) {
          setIsAlreadyPledgeTaken(false);
        } else {
          setIsAlreadyPledgeTaken(true);
        }
      })
      .catch((e) => {
        setIsAlreadyPledgeTaken(false);
      });
  }, []);

  const bannersListWithData = useMemo(
    () =>
      _compact([
        // !isPledgeAlreadyTaken && {
        //   type: BANNER_TYPES.RESOLUTION_BANNER,
        // },
        // !hasFixedRating && { type: BANNER_TYPES.RATING_FIX_BANNER },
        toShowContestBanner && {
          data: featuredContests[0],
          type: BANNER_TYPES.CONTEST_BANNER,
        },
        toShowShowdownBanner && {
          type: BANNER_TYPES.SHOWDOWN_BANNER,
          data: featuredShowdown,
        },
      ]),
    [
      hasFixedRating,
      toShowContestBanner,
      featuredContests,
      toShowShowdownBanner,
      featuredShowdown,
    ],
  );

  const onLayout = useCallback(
    (event) => {
      const { width } = event.nativeEvent.layout;
      setContainerWidth(width);
    },
    [setContainerWidth],
  );

  if (_isEmpty(bannersListWithData)) {
    return null;
  }

  if (loading || isFeaturedShowdownLoading) {
    return <HomeBannerCarouselShimmer />;
  }

  return (
    <View
      onLayout={onLayout}
      style={{ width: isCompactMode ? 'auto' : '100%' }}
    >
      <HomeBannerCarousel
        bannerData={bannersListWithData}
        containerWidth={containerWidth}
      />
    </View>
  );
};

export default React.memo(HomeBannerCarouselContainer);
