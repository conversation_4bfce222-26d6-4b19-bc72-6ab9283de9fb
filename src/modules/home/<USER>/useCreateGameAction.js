import { useCallback, useState } from 'react';
import { stringifyQueryParams } from 'core/utils/general';

import { hideToast, showToast, TOAST_TYPE } from 'molecules/Toast';
import { useRouter } from 'expo-router';
import Analytics from 'core/analytics';
import _isEmpty from 'lodash/isEmpty';
import _find from 'lodash/find';
import {
  GAME_MODES,
  GAME_TYPES as NEW_GAME_TYPES,
} from 'modules/game/constants/game';
import { GAME_TYPES } from 'core/constants/gameTypes';
import useCreateNewGameQuery from './useCreateNewGameQuery';

import { ABILITY_GAME_CONFIG, GAME_CONFIG_KEYS } from '../constants/gameConfig';

import { getGameEvent } from '../utils/gameEvents';
import useChallengeUser from '../../friendsAndFollowers/hooks/mutations/useChallengeUser';

const useCreateGameAction = ({ gameConfig, selectedGameType }) => {
  const router = useRouter();
  const { createGame, creatingGame, error } = useCreateNewGameQuery();

  const [isChallengingFriend, setIsChallengingFriend] = useState(false);

  const { challengeUser } = useChallengeUser();

  const onPressInviteFriend = useCallback(() => {
    const timeLimitKey = gameConfig?.[GAME_CONFIG_KEYS.PLAY_TIME];
    showToast({
      type: TOAST_TYPE.LOADING,
      description: 'Creating Game',
    });
    createGame({ timeLimit: timeLimitKey * 60 }).then((createdGame) => {
      if (_isEmpty(createdGame)) return null;
      const { _id } = createdGame;
      hideToast();
      router.push(`/game/${_id}/play`);
    });
  }, [gameConfig, createGame, router]);

  const onPressPlayOnline = useCallback(() => {
    const timeLimitKey = gameConfig?.[GAME_CONFIG_KEYS.PLAY_TIME];
    const queryParams = {
      timeLimit: timeLimitKey,
      gameType: selectedGameType,
    };
    const stringifiedQueryParams = stringifyQueryParams(queryParams);
    router.push(`/search?${stringifiedQueryParams}`);
  }, [gameConfig, router, selectedGameType]);

  const onPressPlayAbilityDuels = useCallback(() => {
    const timeLimitKey = gameConfig?.[GAME_CONFIG_KEYS.PLAY_TIME];
    const selectedConfig = _find(
      ABILITY_GAME_CONFIG[0].options,
      (option) => option.key === timeLimitKey,
    );
    const timeLimit = selectedConfig.value / 60;

    const queryParams = {
      timeLimit: timeLimitKey,
    };
    const stringifiedQueryParams = stringifyQueryParams(queryParams);
    router.push(
      `/search?${stringifiedQueryParams}&gameType=${selectedGameType}`,
    );
  }, [gameConfig, router, selectedGameType]);

  const onPressPractice = useCallback(() => {
    const timeLimitKey = gameConfig?.[GAME_CONFIG_KEYS.PLAY_TIME];

    createGame({
      timeLimit: timeLimitKey * 60,
      numPlayers: 1,
      gameType: NEW_GAME_TYPES.DMAS,
      gameMode: GAME_MODES.PRACTICE,
    }).then((createdGame) => {
      if (_isEmpty(createdGame)) return null;
      const { _id } = createdGame;
      hideToast();
      router.push(`/practice/${_id}`);
    });

    showToast({
      type: TOAST_TYPE.LOADING,
      description: 'Creating Game',
    });
  }, [gameConfig, router, Analytics]);

  const onPressStartPlayingButton = useCallback(() => {
    const timeLimitKey = gameConfig?.[GAME_CONFIG_KEYS.PLAY_TIME];
    const gameEvent = getGameEvent({ gameType: selectedGameType });

    if (gameEvent) {
      Analytics.track(gameEvent, {
        timeLimit: timeLimitKey,
      });
    }

    switch (selectedGameType) {
      case GAME_TYPES.PLAY_ONLINE: {
        return onPressPlayOnline();
      }
      case GAME_TYPES.PLAY_WITH_FRIEND:
        return onPressInviteFriend();
      case GAME_TYPES.PRACTICE:
        return onPressPractice();
      case GAME_TYPES.FASTEST_FINGER:
        return onPressPlayOnline();
      case GAME_TYPES.ABILITY_DUELS:
        return onPressPlayAbilityDuels();
      default:
        return null;
    }
  }, [
    gameConfig,
    selectedGameType,
    onPressInviteFriend,
    onPressPlayOnline,
    onPressPractice,
    onPressPlayAbilityDuels,
  ]);

  return {
    onPressStartPlayingButton,
    creatingGame,
    error,
  };
};

export default useCreateGameAction;
