import { useCallback } from 'react';
import { gql, useMutation } from '@apollo/client';

import { GAME_FRAGMENT } from '@/src/core/graphql/fragments/game';
import _map from 'lodash/map';
import { decryptJsonData } from 'core/utils/encryptions/decrypt';
import _isEmpty from 'lodash/isEmpty';
import { GAME_MODES, GAME_TYPES } from 'modules/game/constants/game';
import { getGameConfigInputForGame } from 'modules/game/utils/getGameCategoryAndGameTypeInfos';

const CREATE_GAME_QUERY = gql`
  ${GAME_FRAGMENT}
  mutation CreateGame($gameConfig: GameConfigInput) {
    game: createGame(gameConfig: $gameConfig) {
      ...CoreGameFields
    }
  }
`;

const NUM_OF_PLAYERS = 2;
const DEFAULT_TIME_LIMIT = 3 * 60; // 3 minute

const useCreateNewGameQuery = () => {
  const [createGameQuery, { data, loading, error }] =
    useMutation(CREATE_GAME_QUERY);

  const { game: gameData } = data ?? EMPTY_OBJECT;
  const { encryptedQuestions, ...coreGameFields } = gameData ?? EMPTY_OBJECT;
  const questions = _map(encryptedQuestions, decryptJsonData);

  const game = { ...coreGameFields, questions };

  const createGame = useCallback(
    ({
      numPlayers = NUM_OF_PLAYERS,
      timeLimit = DEFAULT_TIME_LIMIT,
      gameType = GAME_TYPES.DMAS,
      gameMode = GAME_MODES.PLAY_VIA_LINK,
      ...restArgs
    } = EMPTY_OBJECT) => {
      const configs =
        gameMode === GAME_MODES.PLAY_VIA_LINK
          ? { numPlayers }
          : { numPlayers, ...restArgs };
      const newGameConfig = getGameConfigInputForGame({
        gameType,
        gameMode,
        timeLimit,
        configs,
      });
      const variables = {
        gameConfig: {
          ...newGameConfig,
        },
      };
      return createGameQuery({ variables }).then((response) => {
        const createdGame = response?.data?.game;
        const { encryptedQuestions, ...coreGameFields } =
          createdGame ?? EMPTY_OBJECT;
        const questions = _map(encryptedQuestions, decryptJsonData);
        return { ...coreGameFields, questions };
      });
    },
    [createGameQuery],
  );
  return {
    game: _isEmpty(gameData) ? gameData : game,
    createGame,
    creatingGame: loading,
    error,
  };
};

export default useCreateNewGameQuery;
