import RIVE_ANIMATIONS from 'core/constants/riveAnimations';
import { ICON_TYPES } from '@/src/components/atoms/Icon';
import dark from '@/src/core/constants/themes/dark';
import { PUZZLE_GAME_TYPES } from 'modules/home/<USER>/puzzleGameTypes';

export const GAME_CATEGORIES = {
  BLITZ: 'BLITZ',
  CLASSIC: 'CLASSIC',
  PUZZLE: 'PUZZLE',
  MEMORY: 'MEMORY',
  ALL: 'ALL',
};

export const GAME_TYPES = {
  MOST_PLAYED: 'MOST_PLAYED',
  PLAY_ONLINE: 'PLAY_ONLINE',
  PLAY_WITH_FRIEND: 'PLAY_WITH_FRIEND',
  PRACTICE: 'PRACTICE',
  ONLINE_CHALLENGE: 'ONLINE_CHALLENGE',
  FLASH_ANZAN: 'FLASH_ANZAN',
  SUMDAY_SHOWDOWN: 'SUMDAY_SHOWDOWN',
  FASTEST_FINGER: 'FASTEST_FINGER',
  GROUP_PLAY: 'GROUP_PLAY',
  ABILITY_DUELS: 'ABILITY_DUELS',
  PUZZLE_DUELS: 'PUZZLE_DUELS',
  DMAS_ABILITY: 'DMAS_ABILITY',
};

export const VALID_GAME_CATEGORIES = [
  GAME_CATEGORIES.ALL,
  GAME_CATEGORIES.BLITZ,
  GAME_CATEGORIES.CLASSIC,
  GAME_CATEGORIES.MEMORY,
  GAME_CATEGORIES.PUZZLE,
];

export const VALID_GAME_CATEGORIES_PLAY_WITH_FRIEND = [
  GAME_CATEGORIES.BLITZ,
  GAME_CATEGORIES.CLASSIC,
  GAME_CATEGORIES.MEMORY,
  GAME_CATEGORIES.PUZZLE,
];

export const GAME_TYPE_COLOURS = {
  [GAME_TYPES.MOST_PLAYED]: '#C86320',
  [GAME_TYPES.PLAY_ONLINE]: '#3177F7',
  [GAME_TYPES.PLAY_WITH_FRIEND]: '#34228f',
  [GAME_TYPES.PRACTICE]: '#0A6F70',
  [GAME_TYPES.ONLINE_CHALLENGE]: '#FF6600',
  [GAME_TYPES.FLASH_ANZAN]: '#8127F7',
  [GAME_TYPES.SUMDAY_SHOWDOWN]: '#FF6600',
  [GAME_TYPES.FASTEST_FINGER]: '#3177F7',
  [GAME_TYPES.ABILITY_DUELS]: '#34228f',
  [GAME_TYPES.GROUP_PLAY]: '#8127F7',
  [GAME_TYPES.PUZZLE_DUELS]: '#0A6F70',
};

export const VALID_GAME_TYPES = [
  GAME_TYPES.MOST_PLAYED,
  GAME_TYPES.PLAY_ONLINE,
  GAME_TYPES.PUZZLE_DUELS,
  GAME_TYPES.ABILITY_DUELS,
  GAME_TYPES.FLASH_ANZAN,
  GAME_TYPES.GROUP_PLAY,
  GAME_TYPES.FASTEST_FINGER,
  GAME_TYPES.PLAY_WITH_FRIEND,
  GAME_TYPES.PRACTICE,
];

export const CATEGORY_WISE_GAME_TYPES = {
  [GAME_CATEGORIES.ALL]: [...VALID_GAME_TYPES],
  [GAME_CATEGORIES.BLITZ]: [
    GAME_TYPES.MOST_PLAYED,
    GAME_TYPES.PLAY_ONLINE,
    GAME_TYPES.PLAY_WITH_FRIEND,
    GAME_TYPES.PRACTICE,
    GAME_TYPES.FASTEST_FINGER,
    GAME_TYPES.GROUP_PLAY,
  ],
  [GAME_CATEGORIES.CLASSIC]: [GAME_TYPES.ABILITY_DUELS],
  [GAME_CATEGORIES.MEMORY]: [GAME_TYPES.FLASH_ANZAN],
  [GAME_CATEGORIES.PUZZLE]: [GAME_TYPES.PUZZLE_DUELS],
};

export const VALID_MOBILE_GAME_TYPES = [
  GAME_TYPES.PLAY_ONLINE,
  GAME_TYPES.FASTEST_FINGER,
  GAME_TYPES.ABILITY_DUELS,
  GAME_TYPES.GROUP_PLAY,
  GAME_TYPES.FLASH_ANZAN,
  GAME_TYPES.PLAY_WITH_FRIEND,
  GAME_TYPES.PRACTICE,
];

export const GAME_RESULT_TYPES = {
  WIN: 'WIN',
  LOSE: 'LOSE',
  DRAW: 'DRAW',
};

export const GAME_TYPE_DETAILS = {
  [GAME_TYPES.MOST_PLAYED]: {
    icon: 'sprint',
    title: '1 MIN DUEL',
    ctaLabel: 'Search Mathlete',
    subtitle: 'Quick-fire math duel',
    tags: [
      {
        title: 'Speed Matters',
        iconConfig: { name: 'forward', type: ICON_TYPES.FONT_AWESOME_5 },
      },
      {
        title: 'PvP',
        iconConfig: { name: 'user-group', type: ICON_TYPES.FONT_AWESOME_6 },
      },
      {
        title: 'Rated',
        iconConfig: { name: 'user-group', type: ICON_TYPES.FONT_AWESOME_6 },
      },
    ],
    gradientColor: dark.colors.GradientCardGreen,
    animationLink: RIVE_ANIMATIONS.ONLINE_DUELS_ANIMATION,
  },
  [GAME_TYPES.PUZZLE_DUELS]: {
    icon: 'fastestFinger',
    title: 'CROSS MATH DUELS',
    ctaLabel: 'Search Mathelete',
    subtitle: '2 MIN',
    tags: [],
    animationLink: RIVE_ANIMATIONS.PUZZLE_DUELS_ANIMATION,
  },
  [GAME_TYPES.PLAY_ONLINE]: {
    icon: 'sprint',
    title: 'ONLINE DUELS',
    ctaLabel: 'Search Mathlete',
    subtitle: 'Quick-fire math duel',
    tags: [
      {
        title: 'Speed Matters',
        iconConfig: { name: 'forward', type: ICON_TYPES.FONT_AWESOME_5 },
      },
      {
        title: 'PvP',
        iconConfig: { name: 'user-group', type: ICON_TYPES.FONT_AWESOME_6 },
      },
      {
        title: 'Rated',
        iconConfig: { name: 'user-group', type: ICON_TYPES.FONT_AWESOME_6 },
      },
    ],
    gradientColor: dark.colors.GradientCardGreen,
    animationLink:
      'https://cdn.matiks.com/files/668cd7fa7f82ca977f9a6d90_running_man_blue.riv?timestamp=1737474942',
  },
  [GAME_TYPES.FLASH_ANZAN]: {
    icon: 'flashAnzan',
    title: 'FLASH ANZAN',
    ctaLabel: 'Search Mathelete',
    subtitle: 'Flash math showdown',
    tags: [
      {
        title: 'Speed Matters',
        iconConfig: { name: 'forward', type: ICON_TYPES.FONT_AWESOME_5 },
      },
      {
        title: 'Invite',
        iconConfig: { name: 'user-group', type: ICON_TYPES.FONT_AWESOME_6 },
      },
    ],
    gradientColor: dark.colors.GradientCardBlue,
    animationLink:
      'https://cdn.matiks.com/files/668cd7fa7f82ca977f9a6d90_flash_anzan.riv?timestamp=1737474876',
  },
  [GAME_TYPES.PLAY_WITH_FRIEND]: {
    icon: 'playWithFriend',
    title: 'PLAY A FRIEND',
    ctaLabel: 'Create Link',
    subtitle: 'Battle your friend',
    tags: [
      {
        title: 'Friendly Duel',
        iconConfig: { name: 'forward', type: ICON_TYPES.FONT_AWESOME_5 },
      },
      {
        title: 'Practice',
        iconConfig: { name: 'user-group', type: ICON_TYPES.FONT_AWESOME_6 },
      },
    ],
    gradientColor: dark.colors.GradientCardGreen,
    animationLink:
      'https://cdn.matiks.com/files/668cd7fa7f82ca977f9a6d90_shake_hand.riv?timestamp=1737474723',
  },
  [GAME_TYPES.PRACTICE]: {
    icon: 'practiceAgainstTime',
    title: 'AGAINST TIME',
    ctaLabel: 'Practice Now',
    subtitle: 'Time challenge mode',
    tags: [
      {
        title: 'Speed Matters',
        iconConfig: { name: 'forward', type: ICON_TYPES.FONT_AWESOME_5 },
      },
      {
        title: 'Invite',
        iconConfig: { name: 'user-group', type: ICON_TYPES.FONT_AWESOME_6 },
      },
    ],
    gradientColor: dark.colors.GradientCardBlue,
    animationLink:
      'https://cdn.matiks.com/files/668cd7fa7f82ca977f9a6d90_clock.riv?timestamp=1737474811',
  },

  [GAME_TYPES.FASTEST_FINGER]: {
    icon: 'fastestFinger',
    title: 'FASTEST FINGERS DUELS',
    ctaLabel: 'Search Mathelete',
    subtitle: 'Fastest Fingers First',
    tags: [
      {
        title: 'Speed Matters',
        iconConfig: { name: 'forward', type: ICON_TYPES.FONT_AWESOME_5 },
      },
      {
        title: 'PvP',
        iconConfig: { name: 'user-group', type: ICON_TYPES.FONT_AWESOME_6 },
      },
      {
        title: 'Rated',
        iconConfig: { name: 'user-group', type: ICON_TYPES.FONT_AWESOME_6 },
      },
    ],
    gradientColor: dark.colors.textLight,
    animationLink:
      'https://cdn.matiks.com/files/668cd7fa7f82ca977f9a6d90_fastest_fingers_first.riv?timestamp=1737474856',
  },
  [GAME_TYPES.GROUP_PLAY]: {
    icon: 'playWithFriend',
    title: 'GROUP PLAY',
    ctaLabel: 'Create Link',
    subtitle: 'Play with group of mathletes',
    tags: [
      {
        title: 'Speed Matters',
        iconConfig: { name: 'forward', type: ICON_TYPES.FONT_AWESOME_5 },
      },
      {
        title: 'Group PvP',
        iconConfig: { name: 'user-group', type: ICON_TYPES.FONT_AWESOME_6 },
      },
    ],
    gradientColor: dark.colors.textLight,
    animationLink:
      'https://cdn.matiks.com/files/66fc19d744b74099e82125b3_group_play.riv?timestamp=1739203710',
  },
  [GAME_TYPES.ABILITY_DUELS]: {
    icon: 'fastestFinger',
    title: 'ABILITY DUELS',
    ctaLabel: 'Search Mathelete',
    subtitle: 'Basic Maths beyond DMAS',
    tags: [
      {
        title: 'LCM, HCF',
        iconConfig: {},
      },
      {
        title: 'Prime Factor',
        iconConfig: {},
      },
      {
        title: 'Sum of Squares',
        iconConfig: {},
      },
    ],
    gradientColor: dark.colors.textLight,
    animationLink: RIVE_ANIMATIONS.ABILITY_DUELS_ANIMATION,
  },

  [PUZZLE_GAME_TYPES.CROSS_MATH_PUZZLE_DUEL]: {
    icon: 'fastestFinger',
    title: 'CROSS MATH PUZZLE DUEL',
    ctaLabel: 'Search Mathelete',
    subtitle: '2 MIN ',
    tags: [
      {
        title: 'PvP',
        iconConfig: { name: 'user-group', type: ICON_TYPES.FONT_AWESOME_6 },
      },
      {
        title: 'Rated',
        iconConfig: { name: 'user-group', type: ICON_TYPES.FONT_AWESOME_6 },
      },
      {
        title: 'Search Mathlete',
        iconConfig: {
          name: 'account-search-outline',
          type: ICON_TYPES.MATERIAL_COMMUNITY_ICONS,
        },
      },
    ],
    gradientColor: dark.colors.GradientCardGreen,
  },
  [PUZZLE_GAME_TYPES.CROSS_MATH_PUZZLE_WITH_FRIEND]: {
    icon: 'fastestFinger',
    title: 'PLAY WITH FRIEND',
    ctaLabel: 'Play With Friend',
    subtitle: '2 MIN ',
    tags: [
      {
        title: 'PvP',
        iconConfig: { name: 'user-group', type: ICON_TYPES.FONT_AWESOME_6 },
      },
      {
        title: 'Rated',
        iconConfig: { name: 'user-group', type: ICON_TYPES.FONT_AWESOME_6 },
      },
      {
        title: 'Invite',
        iconConfig: { name: 'user-group', type: ICON_TYPES.FONT_AWESOME_6 },
      },
    ],
    gradientColor: dark.colors.GradientCardGreen,
  },

  [PUZZLE_GAME_TYPES.CROSS_MATH_PUZZLE_ENDLESS_PRACTICE]: {
    icon: 'fastestFinger',
    title: 'ENDLESS CROSS MATH PUZZLE',
    ctaLabel: 'Play With Friend',
    subtitle: 'Score high without any mistake',
    tags: [
      {
        title: 'Endless',
        iconConfig: {
          name: 'infinite',
          type: ICON_TYPES.IONICON,
        },
      },
      {
        title: 'Score Matters',
        iconConfig: {
          name: 'puzzle-piece',
          type: ICON_TYPES.FONT_AWESOME,
        },
      },
    ],
    gradientColor: dark.colors.GradientCardGreen,
  },
};
