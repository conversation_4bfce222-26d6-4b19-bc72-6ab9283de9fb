import { useCallback, useState } from 'react';

import { GAME_CONFIG_KEYS } from '../constants/gameConfig';
import { GAME_TYPES } from '../constants/gameTypes';

const useGameConfig = (gameType) => {
  const initialConfig =
    gameType === GAME_TYPES.ABILITY_DUELS ||
    gameType === GAME_TYPES.DMAS_ABILITY
      ? {
          [GAME_CONFIG_KEYS.PLAY_TIME]: 2,
        }
      : {
          [GAME_CONFIG_KEYS.PLAY_TIME]: 1,
        };

  const [gameConfig, setGameConfig] = useState(initialConfig);

  const updateGameConfig = useCallback(
    ({ key, value }) => {
      if (!key || !value) return;

      setGameConfig((prevConfig) => ({ ...prevConfig, [key]: value }));
    },
    [setGameConfig],
  );

  return {
    gameConfig,
    updateGameConfig,
  };
};

export default useGameConfig;
