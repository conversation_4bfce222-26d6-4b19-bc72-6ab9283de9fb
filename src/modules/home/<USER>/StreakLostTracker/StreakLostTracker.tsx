import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  getStorageState,
  setStorageItemAsync,
  useStorageState,
} from 'core/hooks/useStorageState';
import { useSession } from 'modules/auth/containers/AuthProvider';
import userReader from 'core/readers/userReader';
import { openBottomSheet } from 'molecules/BottomSheet/BottomSheet';
import dark from 'core/constants/themes/dark';
import useCheckStreakStatus from 'modules/home/<USER>/useCheckStreakStatus';
import _isEmpty from 'lodash/isEmpty';
import StreakLost from './components';
import _isEqual from 'lodash/isEqual';
import Analytics from '@/src/core/analytics';
import { ANALYTICS_EVENTS } from '@/src/core/analytics/const';

const STREAK_LOST_SHOWN_KEY = 'streak-lost-shown';

const StreakLostTracker = ({ streakStatus }: { streakStatus: any }) => {
  const [streakLostShownDate] = useStorageState(STREAK_LOST_SHOWN_KEY);
  const [isInitialized, setIsInitialized] = useState(false);

  const { user } = useSession();
  const userStreak = userReader.longestStreak(user);

  useEffect(() => {
    const checkStorage = async () => {
      const storedDate = await getStorageState(STREAK_LOST_SHOWN_KEY);
      setIsInitialized(true);
    };
    checkStorage();
  }, []);

  const showStreakLostSheet = useCallback(async () => {
    if (!isInitialized) return;

    const today = new Date().toLocaleDateString();

    if (!_isEqual(streakLostShownDate, today)) {
      Analytics.track(ANALYTICS_EVENTS.STREAKS.VIEWED_STREAK_FREEZER_SHEET)
      openBottomSheet({
        content: ({ closeBottomSheet }) => (
          <StreakLost
            closeBottomSheet={closeBottomSheet}
            streakStatus={streakStatus}
          />
        ),
        styles: {
          frame: {
            borderTopColor: dark.colors.streak,
          },
        },
        dismissOnOverlayPress: false,
      });
      await setStorageItemAsync(STREAK_LOST_SHOWN_KEY, today);
    }
  }, [streakLostShownDate, streakStatus, isInitialized]);

  const showStreakLostSheetRef = useRef(showStreakLostSheet);
  showStreakLostSheetRef.current = showStreakLostSheet;

  useEffect(() => {
    if (isInitialized && streakStatus?.canSaveStreak) {
      const timer = setTimeout(() => {
        showStreakLostSheetRef.current();
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [userStreak, streakStatus, isInitialized]);

  return null;
};

const StreakLostTrackerContainer = () => {
  const { streakStatus, loading } = useCheckStreakStatus();

  if (loading || _isEmpty(streakStatus)) {
    return null;
  }

  return <StreakLostTracker streakStatus={streakStatus} />;
};
export default React.memo(StreakLostTrackerContainer);
