import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useRouter } from 'expo-router';
import PropTypes from 'prop-types';
import useMediaQuery from 'core/hooks/useMediaQuery';
import { showRightPane } from 'molecules/RightPane/RightPane';
import { useSession } from 'modules/auth/containers/AuthProvider';
import userReader from 'core/readers/userReader';
import { View } from 'react-native';
import _isEmpty from 'lodash/isEmpty';
import _orderBy from 'lodash/orderBy';
import _filter from 'lodash/filter';
import CompactOnlineUsersSection from './Compact';
import OnlineUsersPage from '../../pages/OnlineUsersPage/OnlineUsersPage';
import useOnlineUsersEventSubscription from '../../hooks/useOnlineUsersEventSubscription';
import useGetPaginatedOnlineUsers from '../../hooks/useGetPaginatedOnlineUsers';

const ONLINE_USERS_PAGE_SIZE = 10;

const OnlineUsers = ({
  onlineUsers,
  isFetchingOnlineUsers,
}: {
  onlineUsers: any[];
  isFetchingOnlineUsers: boolean;
}) => {
  const { isMobile: isCompactMode } = useMediaQuery();

  const router = useRouter();

  const navigateToOnlineUsersPage = useCallback(() => {
    if (!isCompactMode) {
      showRightPane({
        content: <OnlineUsersPage />,
      });
      return;
    }
    router.push('/online-users');
  }, [isCompactMode, router]);

  return (
    <CompactOnlineUsersSection
      key="online-users-section"
      onlineUsers={onlineUsers}
      isFetchingOnlineUsers={isFetchingOnlineUsers}
      navigateToOnlineUsersPage={navigateToOnlineUsersPage}
    />
  );
};

OnlineUsers.propTypes = {
  onlineUsers: PropTypes.array,
};

const OnlineUsersContainer = () => {
  const { user, userId: currUserId } = useSession();
  const { isMobile: isCompactMode } = useMediaQuery();

  // State hooks
  const [onlineUsersData, setOnlineUsersData] = useState<any>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Refs
  const unsubscribeChannelRef = useRef<any>(null);
  const loadInitialDataRef = useRef<any>(null);

  // Custom hooks
  const {
    onlineUsers: wsOnlineUsers,
    unsubscribe,
    isFetchingOnlineUsers,
  } = useOnlineUsersEventSubscription();

  const { fetchOnlineUsers } = useGetPaginatedOnlineUsers({
    pageSize: ONLINE_USERS_PAGE_SIZE,
  });

  // Update refs
  unsubscribeChannelRef.current = unsubscribe;

  const loadInitialData = useCallback(async () => {
    try {
      const response = await fetchOnlineUsers({ pageNumber: 1 });
      const { data } = response ?? EMPTY_OBJECT;
      const { onlineUsers: usersListObject } = data ?? EMPTY_OBJECT;
      const { users = [] } = usersListObject ?? EMPTY_OBJECT;

      const filteredUsers = _orderBy(
        _filter(
          users,
          (user: any) => userReader.id(user?.userInfo) !== currUserId,
        ),
        (user) => userReader.rating(user?.userInfo) || 0,
        'desc',
      );

      setOnlineUsersData(filteredUsers);
    } catch (error) {
      console.error('Error fetching online users:', error);
    } finally {
      setIsLoading(false);
    }
  }, [fetchOnlineUsers, currUserId]);

  loadInitialDataRef.current = loadInitialData;

  useEffect(() => {
    loadInitialDataRef?.current();

    return () => {
      unsubscribeChannelRef?.current?.();
    };
  }, [fetchOnlineUsers, currUserId]);

  useEffect(() => {
    if (!_isEmpty(wsOnlineUsers) && !isFetchingOnlineUsers) {
      setOnlineUsersData(wsOnlineUsers);
    }
  }, [wsOnlineUsers, isFetchingOnlineUsers]);

  const needToShowOnlineUsers = userReader.needToShowOnlineUsers(user);

  // Move the condition to the render return
  if (!needToShowOnlineUsers) {
    return <View style={{ height: isCompactMode ? 12 : 0 }} />;
  }

  return (
    <OnlineUsers
      onlineUsers={onlineUsersData}
      isFetchingOnlineUsers={isLoading}
    />
  );
};

export default React.memo(OnlineUsersContainer);
