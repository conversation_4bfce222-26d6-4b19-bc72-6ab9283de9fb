import React from 'react';
import { useSession } from 'modules/auth/containers/AuthProvider';
import useRefetchOnFocusChange from 'core/hooks/useReFetchOnFocusChange';
import useMediaQuery from 'core/hooks/useMediaQuery';
import StreakMilestone from 'modules/home/<USER>/StreakMilestone';
import CompactHomeLayout from './CompactHomeLayout';
import ExpandedHomeLayout from './ExpandedHomeLayout';
import StreakLostTracker from '../../components/StreakLostTracker';

const HomeLayout = (props) => {
  const { isMobile: isCompactDevice } = useMediaQuery();

  const { refreshCurrentUser } = useSession();

  useRefetchOnFocusChange(refreshCurrentUser);

  if (isCompactDevice) {
    return <CompactHomeLayout {...props} />;
  }

  return <ExpandedHomeLayout {...props} />;
};

HomeLayout.propTypes = {};

const HomeLayoutContainer = () => (
  <>
    <StreakMilestone />
    <StreakLostTracker />
    <HomeLayout />
  </>
);

export default React.memo(HomeLayoutContainer);
