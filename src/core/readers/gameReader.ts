import _property from 'lodash/property';
import _get from 'lodash/get';
import { GAME_CATEGORY, GAME_MODES } from 'modules/game/constants/game';

const getTimeLimitOfGame = (gameObj: any) => {
  const { config } = gameObj ?? EMPTY_OBJECT;
  const gameCategory = _get(gameObj, 'gameCategory');
  const categorySpecificGameConfig = _get(
    config,
    'categorySpecificConfig',
    EMPTY_OBJECT,
  );

  const timeLimit = _get(config, 'timeLimit');

  switch (gameCategory) {
    case GAME_CATEGORY.BLITZ:
      return _get(
        categorySpecificGameConfig,
        ['blitz', 'timeLimit'],
        timeLimit,
      );
    case GAME_CATEGORY.CLASSICAL:
      return _get(
        categorySpecificGameConfig,
        ['classical', 'timeLimit'],
        timeLimit,
      );
    case GAME_CATEGORY.MEMORY:
      return _get(
        categorySpecificGameConfig,
        ['memory', 'timeLimit'],
        timeLimit,
      );
    case GAME_CATEGORY.PUZZLE:
      return _get(
        categorySpecificGameConfig,
        ['puzzle', 'timeLimit'],
        timeLimit,
      );
    default:
      return _get(config, 'timeLimit');
  }
};

const getNumPlayersOfGame = (gameObj: any) => {
  const { config } = gameObj ?? EMPTY_OBJECT;
  const gameMode = _get(gameObj, 'gameMode');
  const gameModeSpecificGameConfig = _get(
    config,
    'modeSpecificConfig',
    EMPTY_OBJECT,
  );

  const numPlayers = _get(config, 'numPlayers');

  switch (gameMode) {
    case GAME_MODES.ONLINE_SEARCH:
      return _get(
        gameModeSpecificGameConfig,
        ['onlineSearch', 'numPlayers'],
        numPlayers,
      );
    case GAME_MODES.ONLINE_CHALLENGE:
      return _get(
        gameModeSpecificGameConfig,
        ['onlineChallenge', 'numPlayers'],
        numPlayers,
      );
    case GAME_MODES.GROUP_PLAY:
      return _get(
        gameModeSpecificGameConfig,
        ['groupPlay', 'numPlayers'],
        numPlayers,
      );
    case GAME_MODES.PRACTICE:
      return _get(
        gameModeSpecificGameConfig,
        ['practice', 'numPlayers'],
        numPlayers,
      );
    case GAME_MODES.RUSH_WITH_TIME:
      return _get(
        gameModeSpecificGameConfig,
        ['rushWithTime', 'numPlayers'],
        numPlayers,
      );
    case GAME_MODES.RUSH_WITHOUT_TIME:
      return _get(
        gameModeSpecificGameConfig,
        ['rushWithoutTime', 'numPlayers'],
        numPlayers,
      );
    case GAME_MODES.PLAY_VIA_LINK:
      return _get(
        gameModeSpecificGameConfig,
        ['playViaLink', 'numPlayers'],
        numPlayers,
      );
    case GAME_MODES.SUMDAY_SHOWDOWN:
      return _get(
        gameModeSpecificGameConfig,
        ['sumdayShowdown', 'numPlayers'],
        numPlayers,
      );
    case GAME_MODES.SURVIVAL_SATURDAY:
      return _get(
        gameModeSpecificGameConfig,
        ['survivalSaturday', 'numPlayers'],
        numPlayers,
      );
    default:
      return _get(config, 'numPlayers');
  }
};

const getGameModeSpecificConfig = (gameObj: any) => {
  const { config } = gameObj ?? EMPTY_OBJECT;
  const gameMode = _get(gameObj, 'gameMode');
  const gameModeSpecificGameConfig = _get(
    gameObj,
    'modeSpecificConfig',
    EMPTY_OBJECT,
  );

  const modeSpecificConfig = _get(config, 'config');

  switch (gameMode) {
    case GAME_MODES.ONLINE_SEARCH:
      return _get(
        gameModeSpecificGameConfig,
        'onlineSearch',
        modeSpecificConfig,
      );
    case GAME_MODES.ONLINE_CHALLENGE:
      return _get(
        gameModeSpecificGameConfig,
        'onlineChallenge',
        modeSpecificConfig,
      );
    case GAME_MODES.GROUP_PLAY:
      return _get(gameModeSpecificGameConfig, 'groupPlay', modeSpecificConfig);
    case GAME_MODES.PRACTICE:
      return _get(gameModeSpecificGameConfig, 'practice', modeSpecificConfig);
    case GAME_MODES.RUSH_WITH_TIME:
      return _get(
        gameModeSpecificGameConfig,
        'rushWithTime',
        modeSpecificConfig,
      );
    case GAME_MODES.RUSH_WITHOUT_TIME:
      return _get(
        gameModeSpecificGameConfig,
        'rushWithoutTime',
        modeSpecificConfig,
      );
    case GAME_MODES.PLAY_VIA_LINK:
      return _get(
        gameModeSpecificGameConfig,
        'playViaLink',
        modeSpecificConfig,
      );
    case GAME_MODES.SUMDAY_SHOWDOWN:
      return _get(
        gameModeSpecificGameConfig,
        'sumdayShowdown',
        modeSpecificConfig,
      );
    case GAME_MODES.SURVIVAL_SATURDAY:
      return _get(
        gameModeSpecificGameConfig,
        'survivalSaturday',
        modeSpecificConfig,
      );
    default:
      return _get(config, 'config');
  }
};

export default {
  id: _property('_id'),
  createdBy: _property('createdBy'),
  gameStatus: _property('gameStatus'),
  config: getGameModeSpecificConfig,
  leaderBoard: _property('leaderBoard'),
  players: _property('players'),
  gameMode: _property('gameMode'),
  gameCategory: _property('gameCategory'),
  startTime: (gameObj: any) => _get(gameObj, 'startTime'),
  endTime: (gameObj: any) => _get(gameObj, 'endTime'),
  gameType: (gameObj: any) => _get(gameObj, 'gameType'),

  // Category Specific
  timeLimit: getTimeLimitOfGame,

  // Mode Specific
  numPlayers: getNumPlayersOfGame,
  modeSpecificConfig: getGameModeSpecificConfig,
};
