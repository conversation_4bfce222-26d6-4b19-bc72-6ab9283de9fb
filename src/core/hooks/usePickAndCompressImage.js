import { useCallback, useState } from 'react';
import * as ImageManipulator from 'expo-image-manipulator';
import { launchImageLibraryAsync } from 'expo-image-picker';
import _isEmpty from 'lodash/isEmpty';

const MAX_FILE_SIZE = 102400;
const EMPTY_OBJECT = {};

const getFileInfo = async (uri) => {
  try {
    const response = await fetch(uri);
    const blob = await response.blob();
    return { size: blob.size };
  } catch (error) {
    console.error('Error getting file info:', error);
    return { size: 0 };
  }
};

const usePickAndCompressImage = () => {
  const [imageUri, setImageUri] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [compressionStatus, setCompressionStatus] = useState('');

  const compressImage = useCallback(async (uri) => {
    try {
      setCompressionStatus('Analyzing image...');
      let quality = 0.7;
      const compressedUri = uri;
      let fileInfo = await getFileInfo(uri);

      setCompressionStatus('Initial compression...');
      let compressed = await ImageManipulator.manipulateAsync(
        uri,
        [{ resize: { width: 200 } }],
        { compress: quality, format: ImageManipulator.SaveFormat.JPEG },
      );

      fileInfo = await getFileInfo(compressed.uri);

      if (fileInfo.size > MAX_FILE_SIZE) {
        setCompressionStatus('Optimizing quality...');
        while (fileInfo.size > MAX_FILE_SIZE && quality > 0.1) {
          quality -= 0.1;
          compressed = await ImageManipulator.manipulateAsync(
            uri,
            [{ resize: { width: 200 } }],
            { compress: quality, format: ImageManipulator.SaveFormat.JPEG },
          );
          fileInfo = await getFileInfo(compressed.uri);
        }
      }

      if (fileInfo.size > MAX_FILE_SIZE) {
        setCompressionStatus('Adjusting dimensions...');
        let width = 800;
        while (fileInfo.size > MAX_FILE_SIZE && width > 200) {
          width -= 100;
          compressed = await ImageManipulator.manipulateAsync(
            uri,
            [{ resize: { width } }],
            { compress: quality, format: ImageManipulator.SaveFormat.JPEG },
          );
          fileInfo = await getFileInfo(compressed.uri);
        }
      }

      setCompressionStatus('');
      return compressed.uri;
    } catch (error) {
      console.error('Image compression failed:', error);
      setCompressionStatus('');
      return uri;
    }
  }, []);

  const pickAndCompressImage = useCallback(async () => {
    try {
      setIsLoading(true);
      const result = await launchImageLibraryAsync({
        mediaTypes: 'Images',
        allowsEditing: true,
        quality: 1,
      });

      const { assets } = result ?? EMPTY_OBJECT;
      if (!_isEmpty(assets) && !_isEmpty(assets[0]?.uri)) {
        const compressedUri = await compressImage(assets[0].uri);
        setImageUri(compressedUri);
        return compressedUri;
      }
    } catch (error) {
      setIsLoading(false);
      console.error('Image selection failed:', error);
    } finally {
      setIsLoading(false);
    }
    return null;
  }, [compressImage]);

  return {
    imageUri,
    isLoading,
    compressionStatus,
    compressImage,
    pickAndCompressImage,
  };
};

export default usePickAndCompressImage;
