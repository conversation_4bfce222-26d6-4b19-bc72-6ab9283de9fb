import { useEffect, useRef, useState } from 'react';
import { ensureSplashScreenShown } from 'core/utils/splashScreenHelper';
import _isNil from 'lodash/isNil';
import useApolloClientSetup from './useApolloClientSetup';
import useLoadFontsAsync from './useLoadFontsAsync';
import useCurrentUserCache from '../useCurrentUserCache';
import useRefetchOnAppFocus from '../useRefetchOnAppFocus';
import Analytics from '../../analytics';
import { ANALYTICS_EVENTS } from '../../analytics/const';

let isAppInitialized = false;

const useAppInitialize = () => {
  const {
    apiToken,
    apolloClient,
    clientCreationError,
    initializeApolloClient,
    updateApolloClient,
  } = useApolloClientSetup();
  const { initializeFontAsync } = useLoadFontsAsync();
  const { getUserFromCache } = useCurrentUserCache();

  const [isAppReady, setIsAppReady] = useState(false);
  const [cachedUser, setCachedUser] = useState(null);

  const timeoutRef = useRef(null);
  const eventTimeoutRef = useRef(null);
  const isAppReadyRef = useRef(isAppReady);
  isAppReadyRef.current = isAppReady;

  const reInitializeApolloClientRef = useRef(initializeApolloClient);
  reInitializeApolloClientRef.current = initializeApolloClient;
  const apolloClientRef = useRef(apolloClient);
  apolloClientRef.current = apolloClient;
  const setIsAppReadyRef = useRef(setIsAppReady);
  setIsAppReadyRef.current = setIsAppReady;

  useRefetchOnAppFocus(() => {
    if (!isAppInitialized) return;
    if (!_isNil(apolloClientRef.current)) return;

    reInitializeApolloClientRef.current?.().then(() => {
      setIsAppReadyRef.current?.(true);
    });
  });

  useEffect(() => {
    if (!_isNil(apolloClientRef.current)) {
      clearTimeout(eventTimeoutRef.current);
      return;
    }

    eventTimeoutRef.current = setTimeout(() => {
      if (_isNil(apolloClientRef.current)) {
        Analytics.track(ANALYTICS_EVENTS.APP_INFINITE_LOADING_ERROR);
      }
    }, 5 * 1000);

    return () => {
      if (eventTimeoutRef.current) {
        clearTimeout(eventTimeoutRef.current);
      }
    };
  }, [isAppReady]);

  useEffect(
    () => () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    },
    [],
  );

  useEffect(() => {
    // Ensure splash screen is shown before we start loading resources
    ensureSplashScreenShown().then(() => {
      Promise.all([
        initializeFontAsync(),
        initializeApolloClient(),
        getUserFromCache(),
      ])
        .then((results) => {
          const [font, client, user] = results;
          setCachedUser(user);
        })
        .catch((error) => {
          Bugsnag.notify(error);
          console.error('Error during app initialization:', error);
        })
        .finally(() => {
          setIsAppReady(true);
          isAppInitialized = true;
        });
    });
  }, []);

  return {
    apiToken,
    cachedUser,
    apolloClient,
    clientCreationError,
    initializeApolloClient,
    updateApolloClient,
    isAppReady,
  };
};

export default useAppInitialize;
