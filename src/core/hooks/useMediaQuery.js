import { useMemo } from 'react';
import { Platform } from 'react-native';
import { useMediaQuery as useRNMediaQuery } from 'react-responsive';

const useMediaQuery = () => {
  const isExpandedWindow = useRNMediaQuery({ query: '(min-width: 1500px)' });
  const isRegularWindow = useRNMediaQuery({ query: '(max-width: 1224px)' });
  const isDesktop = useRNMediaQuery({ minWidth: 1025 });
  const isTablet = useRNMediaQuery({ minWidth: 520, maxWidth: 1024 });
  const isMobile = useRNMediaQuery({ maxWidth: 520 }) || Platform.OS !== 'web';
  const isMobileBrowser = isMobile && Platform.OS === 'web';
  const isWebBrowser = !isMobile && Platform.OS === 'web';
  const isTabletBrowser = isTablet && Platform.OS === 'web';
  const isDesktopBrowser = isDesktop && Platform.OS === 'web';

  return useMemo(
    () => ({
      isExpandedWindow,
      isRegularWindow,
      isDesktop,
      isTablet,
      isMobile,
      isWebBrowser,
      isMobileBrowser,
      isTabletBrowser,
      isDesktopBrowser,
    }),
    [
      isExpandedWindow,
      isRegularWindow,
      isDesktop,
      isTablet,
      isMobile,
      isWebBrowser,
      isMobileBrowser,
      isTabletBrowser,
      isDesktopBrowser,
    ],
  );
};

export default useMediaQuery;
