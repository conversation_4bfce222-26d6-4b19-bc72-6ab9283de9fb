export const EVENTS = {
  FRIENDS_AND_FOLLOWERS: {
    VIEWED_FRIENDS_PAGE: 'view friends page',
    VIEWED_FOLLOWERS_PAGE: 'view followers page',
    VIEWED_FOLLOWINGS_PAGE: 'view followings page',
    VIEWED_PENDING_REQUESTS_TAB: 'view pending request tab',
    VIEWED_FRIENDS_TAB: 'view friends list tab',
    CLICKED_ON_SEARCH_FRIEND: 'click on search friend',
    CLICKED_ON_USER_PROFILE_FROM_FOLLOWERS_PAGE:
      'click on user profile card from followers page',
    CLICKED_ON_USER_PROFILE_FROM_FOLLOWINGS_PAGE:
      'click on user profile card from followings page',
    CLICKED_ON_USER_PROFILE_FROM_FRIENDS_TAB:
      'click on user profile card from friends tab',
    CLICKED_ON_USER_PROFILE_FROM_PENDING_REQUESTS_TAB:
      'click on user profile card from pending friend request tab',
    CLICKED_ON_USER_PROFILE_FROM_SEARCH_FREIND_LIST:
      'click on user profile card from searched friends list',
    CLICKED_ON_FOLLOW_USER_FROM_PROFILE_VIEW:
      'click on follow user from user public profile',
    CLICKED_ON_UNFOLLOW_USER_FROM_PROFILE_VIEW:
      'click on unfollow user from user public profile',
    CLICKED_ON_SEND_FRIEND_REQUEST_FROM_PROFILE_VIEW:
      'click on send friend request from user public profile',
    CLICKED_ON_WITHDRAW_FRIEND_REQUEST_FROM_PROFILE_VIEW:
      'click on withdraw friend request from user public profile',
    CLICKED_ON_ACCEPT_FRIEND_REQUEST_FROM_PROFILE_VIEW:
      'click on accept friend request from user public profile',
    CLICKED_ON_REJECT_FRIEND_REQUEST_FROM_PROFILE_VIEW:
      'click on reject friend request from user public profile',
    CLICKED_ON_REMOVE_FRIEND_FROM_PROFILE_VIEW:
      'click on remove friend from user public profile',
    CLICKED_ON_CHALLENGE_FRIEND_FROM_PROFILE_VIEW:
      'click on challenge friend from user public profile',
    CLICKED_ON_ACCEPT_FRIEND_REQUEST: 'click on accept friend request',
    CLICKED_ON_REJECT_FRIEND_REQUEST: 'click on reject friend request',
    CLICKED_ON_REMOVE_FRIEND: 'click on remove friend',
    CLICKED_ON_REMOVE_FOLLOWER: 'click on remove follower',
    CLICKED_ON_REMOVE_FOLLOWING: 'click on remove following',
    CLICKED_ON_FOLLOWERS_COUNT_TEXT: 'click on followers count text',
    CLICKED_ON_FOLLOWINGS_COUNT_TEXT: 'click on followings count text',
    CLICKED_ON_FRIENDS_COUNT_TEXT: 'click on friends count text',
    CLICKED_ON_CHALLENGE_FRIEND: 'click on challenge friend',
    CLICKED_ON_SHARE_PROFILE_CARD: 'click on share profile card',
    CLICKED_ON_USER_CHAT_ACTION: 'click on user chat action',
    CLICKED_ON_FIND_FRIEND_SHARE_PROFILE:
      'find friend: click on share profile card button',
  },
};
