export const events: Record<string, string> = {
  UserMatchedEvent: 'UserMatchedEvent',
  RematchRequestedEvent: 'RematchRequestedEvent',
  BadgeAssignedEvent: 'BadgeAssignedEvent',
  ShowdownFicturesCreated: 'ShowdownFicturesCreated',
  ShowdownToStart: 'ShowdownToStart',
  ShowdownParticipantUpdatedEvent: 'ShowdownParticipantUpdatedEvent',
  ChallengeUserEvent: 'ChallengeUserEnumStruct',
  GameCanceledEvent: 'GameCanceledEvent',
  ChallengeForPuzzleGameEvent: 'ChallengeForPuzzleGameOutput',
  PuzzleGameEventWithOpponentOutput: 'PuzzleGameEventWithOpponentOutput',
  RatingFixtureEvent: 'RatingFixtureEvent',
  StatikCoinsEarnedEvent: 'StatikCoinsEarnedEvent',
  StreakMaintainedEvent: 'StreakMaintainedEvent',
  MessagePublishedEvent: 'MessagePublishedEvent',
  SearchTimeoutEvent: 'SearchTimeoutEvent',
  JoinedWeeklyLeagueEvent: 'JoinedWeeklyLeagueEvent',
  InAppNotification: 'InAppNotification',
};

export const listenersNamespace = {
  InAppNotification: 'InAppNotification',
  StatikCoinsEarnedEvent: 'StatikCoinsEarnedEvent',
  UserMatchedEvent: 'UserMatchedEvent',
  RematchRequestedEvent: 'RematchRequestedEvent',
  ChallengeUserEvent: 'ChallengeUserEvent',
  GameCanceledEvent: 'GameCanceledEvent',
  ChallengeForPuzzleGameEvent: 'ChallengeForPuzzleGameEvent',
  StreakMaintainedEvent: 'StreakMaintainedEvent',
  JoinedWeeklyLeagueEvent: 'JoinedWeeklyLeagueEvent',
  BadgeAssignedEvent: 'BadgeAssignedEvent',
  RatingFixtureEvent: 'RatingFixtureEvent',
  MessagePublishedEvent: 'MessagePublishedEvent',
  ShowdownFicturesCreated: 'ShowdownFicturesCreated',
  ShowdownParticipantUpdatedEvent: 'ShowdownParticipantUpdatedEvent',
  GameEvent: 'GameEvent',
};
