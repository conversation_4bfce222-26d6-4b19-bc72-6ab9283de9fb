import { addSeconds, intervalToDuration } from 'date-fns';

// eslint-disable-next-line import/prefer-default-export
export const formatTime = (milliSecondsInput: number) => {
  const secondsInput = milliSecondsInput / 1000;

  if (milliSecondsInput <= 0) {
    return '00:00';
  }

  if (secondsInput < 3600) {
    const minutes = Math.floor(secondsInput / 60)
      .toString()
      .padStart(2, '0');
    const seconds = Math.floor(secondsInput % 60)
      .toString()
      .padStart(2, '0');

    return `${minutes}:${seconds}`;
  }

  if (secondsInput < 86400) {
    // Less than one day (24 * 60 * 60 seconds)
    // Calculate hours, minutes, and seconds
    const hours = Math.floor(secondsInput / 3600)
      .toString()
      .padStart(2, '0');
    const minutes = Math.floor((secondsInput % 3600) / 60)
      .toString()
      .padStart(2, '0');
    const seconds = Math.floor(secondsInput % 60)
      .toString()
      .padStart(2, '0');

    return `${hours}:${minutes}:${seconds}`;
  }
  // More than one day
  const duration = intervalToDuration({
    start: new Date(0),
    end: addSeconds(new Date(0), secondsInput),
  });

  if (duration.years && duration.years >= 1) {
    return `${duration.years} ${duration.years === 1 ? 'year' : 'years'}`;
  }
  if (duration.months && duration.months >= 1) {
    return `${duration.months} ${duration.months === 1 ? 'month' : 'months'}`;
  }
  if (duration.days && duration.days >= 1) {
    return `${duration.days} ${duration.days === 1 ? 'day' : 'days'}`;
  }
  // Fallback for durations less than a day, just in case
  return '1 day';
};
