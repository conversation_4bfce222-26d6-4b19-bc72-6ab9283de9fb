import _includes from 'lodash/includes';
import _toNumber from 'lodash/toNumber';
import { GAME_TYPES } from 'core/constants/gameTypes';

const isValidTimeLimit = ({ timeLimit, gameType }) => {
  const timeLimitInt = _toNumber(timeLimit);
  switch (gameType) {
    case GAME_TYPES.FLASH_ANZAN:
      return timeLimitInt === 1.5;
    case GAME_TYPES.ABILITY_DUELS:
      return _includes([2, 3, 5, 10], timeLimitInt);
    case GAME_TYPES.DMAS_ABILITY:
      return _includes([2, 3, 5, 10], timeLimitInt);
    default:
      return _includes([1, 2, 3, 5], timeLimitInt);
  }
};

export default isValidTimeLimit;
