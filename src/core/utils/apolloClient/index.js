/* eslint-disable import/prefer-default-export */
import {
  ApolloClient,
  ApolloLink,
  InMemoryCache,
  makeVar,
} from '@apollo/client';

import { onError } from '@apollo/client/link/error';

import {
  getStorageState,
  setStorageItemAsync,
} from 'core/hooks/useStorageState';
import _includes from 'lodash/includes';
import { Platform } from 'react-native';
import uuid from 'react-native-uuid';
import DeviceInfo from 'react-native-device-info';
import { createUploadLink } from 'apollo-upload-client';
import { Observable } from 'apollo-link';
import { SERVER_URL } from '../../constants/graphql';
import Analytics from '../../analytics';
import { ANALYTICS_EVENTS } from '../../analytics/const';

const UNAUTHENTICATED_OPERATIONS = [
  'SendOTP',
  'GoogleLogin',
  'LegacyGoogleLogin',
  'SignInWithApple',
  'LoginAsGuest',
  'AppleLogin',
];

const DEVICE_ID_PREFIX = `${Platform?.OS ?? 'unknown'}`;
const getRequestId = () => {
  const _id = uuid?.v4() ?? `${Date.now()}`;
  return `${DEVICE_ID_PREFIX}_${_id}`;
};

const getDeviceInfo = () => ({
  deviceType: Platform.OS,
  deviceId: DeviceInfo?.getUserAgentSync() ?? DeviceInfo?.getDeviceId() ?? '',
  appVersion: DeviceInfo?.getVersion() ?? '',
  osVersion: DeviceInfo?.getSystemVersion() ?? '',
});

export const authToken = makeVar(null);

export const createApolloClient = async ({
  newToken,
  onUnAuthorizationError,
}) => {
  const DEVICE_INFO = getDeviceInfo();
  const _authToken = await getStorageState('session');
  authToken(_authToken);
  const uploadLink = createUploadLink({
    uri: SERVER_URL,
    credentials: 'include',
  });

  const authLink = new ApolloLink((operation, forward) => {
    const requestId = getRequestId();
    operation.setContext({
      headers: {
        authorization: authToken() ? `Bearer ${authToken()}` : '',
        'X-Timezone': Intl.DateTimeFormat().resolvedOptions().timeZone,
        'X-Request-ID': requestId,
      },
      requestId,
    });
    return forward(operation);
  });

  const queryGatekeeper = new ApolloLink((operation, forward) => {
    const operationName = operation?.operationName;

    if (!authToken() && !_includes(UNAUTHENTICATED_OPERATIONS, operationName)) {
      return new Observable((observer) => {
        observer.complete(); // silent drop
      });
    }
    return forward(operation);
  });

  const errorLink = onError(({ graphQLError, networkError, operation }) => {
    if (networkError && networkError.statusCode === 401) {
      authToken(null); // Clear the token in memory
      setStorageItemAsync('session', null); // Remove token from storage
      onUnAuthorizationError?.();
    }
  });

  const loggingLink = new ApolloLink((operation, forward) => {
    const requestId = operation?.getContext?.()?.requestId;
    const { operationName } = operation;
    return forward(operation).map((response) => {
      if (response.errors && response.errors.length > 0) {
        printDebug('API Error:', {
          requestId,
          operationName,
          errors: response.errors,
        });

        Analytics.track(ANALYTICS_EVENTS.API_ERROR, {
          requestId,
          operationName,
          errors: response.errors,
          deviceInfo: DEVICE_INFO,
        });
      }
      return response;
    });
  });

  const combinedLink = ApolloLink.from([
    errorLink,
    authLink,
    queryGatekeeper,
    loggingLink,
    uploadLink,
  ]);

  return new ApolloClient({
    link: combinedLink,
    cache: new InMemoryCache(),
    defaultOptions: {
      watchQuery: {
        fetchPolicy: 'cache-and-network',
      },
    },
  });
};
