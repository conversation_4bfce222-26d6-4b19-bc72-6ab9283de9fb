import { gql } from '@apollo/client';

// TODO @rishav remove comments
export const USER_FRAGMENT = gql`
  fragment CoreUserFields on User {
    _id
    name
    profileImageUrl
    rating
    ratingV2 {
      flashAnzanRating
      globalRating
      abilityDuelsRating
      puzzleRating
    }
    statikCoins
    username
    countryCode
    isGuest
    bio
    hasFixedRating
    globalRank
    countryRank
    userStreaks {
      currentStreak
      longestStreak
      lastSevenDays
    }
    stats {
      followersCount
      followingsCount
      friendsCount
      ngp
      hr
    }
    awardsAndAchievements {
      imageUrl
      link
      title
      description
    }
    links
    league {
      league
      hasParticipated
      coinsTillLastWeek
    }
    badge
    isDeleted
    institutionId
    institutionName
  }
`;

export const CURRENT_USER_FRAGMENT = gql`
  fragment CurrentUserFields on User {
    _id
    name
    username
    email
    token
    timezone
    profileImageUrl
    rating
    ratingV2 {
      flashAnzanRating
      globalRating
      abilityDuelsRating
      puzzleRating
    }
    statikCoins
    hasFixedRating
    countryCode
    isGuest
    bio
    globalRank
    additional {
      timeSpent
      hasUnlockedAllGames
    }
    countryRank
    userStreaks {
      currentStreak
      longestStreak
      lastSevenDays
      streakFreezers
    }
    stats {
      followersCount
      followingsCount
      friendsCount
      ngp
      hr
    }
    badge
    awardsAndAchievements {
      imageUrl
      link
      title
      description
    }
    league {
      league
      hasParticipated
      coinsTillLastWeek
    }
    links
    referralCode
    isReferred
    lastReadFeedId
    isDeleted
    isSignup
    institutionId
    institutionName
  }
`;
