import React from 'react';
import { View } from 'react-native';
import dark from 'core/constants/themes/dark';
import Icon, { ICON_TYPES } from 'atoms/Icon';

const DefaultCollegeIcon = ({ size = 40 }: { size?: number }) => (
  <View
    style={{
      width: size,
      height: size,
      borderRadius: 5,
      justifyContent: 'center',
      alignItems: 'center',
    }}
  >
    <Icon
      name="school-outline"
      type={ICON_TYPES.IONICON}
      size={size}
      color={dark.colors.textLight}
    />
  </View>
);

export default DefaultCollegeIcon;
