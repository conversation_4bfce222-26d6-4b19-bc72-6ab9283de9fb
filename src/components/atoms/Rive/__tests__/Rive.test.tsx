import React from 'react';
import { render } from '@testing-library/react-native';
import { validateRiveFile, validateRiveUrl } from '../utils/riveUtils';

jest.mock('core/analytics', () => ({
  track: jest.fn(),
}));

jest.mock(
  'rive-react-native',
  () =>
    function MockRive(props: any) {
      const React = require('react');
      const { View, Text } = require('react-native');

      React.useEffect(() => {
        if (props.onLoad) {
          props.onLoad();
        }
      }, []);

      return React.createElement(
        View,
        { style: props.style },
        React.createElement(Text, {}, 'Mock Rive Animation'),
      );
    },
);

// Mock AppState
jest.mock('react-native/Libraries/AppState/AppState', () => ({
  currentState: 'active',
  addEventListener: jest.fn(() => ({ remove: jest.fn() })),
}));

// Import components after mocks
const RiveComponent = require('../Rive.native').default;
const RiveErrorBoundary = require('../RiveErrorBoundary').default;
const withRiveErrorBoundary = require('../withRiveErrorBoundary').default;

describe('RiveComponent', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    const component = render(
      <RiveComponent
        url="https://cdn.rive.app/animations/vehicles.riv"
        artboardName="Artboard"
        stateMachineName="State Machine 1"
      />,
    );
    expect(component).toBeTruthy();
  });

  it('shows fallback text for invalid URL', () => {
    const { getByText } = render(
      <RiveComponent
        url="invalid-url"
        artboardName="Artboard"
        stateMachineName="State Machine 1"
        fallbackText="Custom fallback"
      />,
    );

    expect(getByText('Custom fallback')).toBeTruthy();
  });

  it('shows default fallback text when no custom text provided', () => {
    const { getByText } = render(
      <RiveComponent
        url="invalid-url"
        artboardName="Artboard"
        stateMachineName="State Machine 1"
      />,
    );

    expect(getByText('Animation unavailable')).toBeTruthy();
  });
});

describe('Rive Utils', () => {
  describe('validateRiveUrl', () => {
    it('validates correct Rive URLs', () => {
      expect(validateRiveUrl('https://cdn.rive.app/animations/test.riv')).toBe(
        true,
      );
      expect(validateRiveUrl('http://example.com/animation.riv')).toBe(true);
    });

    it('rejects invalid URLs', () => {
      expect(validateRiveUrl('')).toBe(false);
      expect(validateRiveUrl('invalid-url')).toBe(false);
      expect(validateRiveUrl('https://example.com/test.json')).toBe(false);
      expect(validateRiveUrl('ftp://example.com/test.riv')).toBe(false);
    });
  });

  describe('validateRiveFile', () => {
    beforeEach(() => {
      global.fetch = jest.fn();
    });

    afterEach(() => {
      jest.restoreAllMocks();
    });

    it('validates accessible files', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        headers: {
          get: jest.fn().mockReturnValue('1024'),
        },
      });

      const result = await validateRiveFile('https://example.com/test.riv');
      expect(result.isValid).toBe(true);
      expect(result.fileSize).toBe(1024);
    });

    it('handles network errors', async () => {
      (global.fetch as jest.Mock).mockRejectedValueOnce(
        new Error('Network error'),
      );

      const result = await validateRiveFile('https://example.com/test.riv');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Network error');
    });

    it('handles HTTP errors', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: 'Not Found',
      });

      const result = await validateRiveFile('https://example.com/test.riv');
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('HTTP 404: Not Found');
    });

    it('handles file size limits', async () => {
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        headers: {
          get: jest.fn().mockReturnValue('10485760'), // 10MB
        },
      });

      const result = await validateRiveFile('https://example.com/test.riv', {
        maxFileSize: 1024 * 1024, // 1MB limit
      });
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('File too large');
    });
  });
});
