import React, { useCallback, useEffect, useRef, useState } from 'react';
import Rive from 'rive-react-native';
import { AppState, Text, View, ViewStyle } from 'react-native';
import dark from 'core/constants/themes/dark';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';

interface RiveComponentProps {
  url: string;
  resourceName?: string;
  artboardName: string;
  stateMachineName: string;
  style?: ViewStyle;
  autoPlay?: boolean;
  loop?: boolean;
  onLoopEnd?: () => void;
  fallbackText?: string;
  maxRetries?: number;
  retryDelay?: number;
  enableCrashPrevention?: boolean;
}

interface RiveState {
  hasError: boolean;
  isLoading: boolean;
  retryCount: number;
  errorType: 'network' | 'file' | 'runtime' | 'unknown';
  lastError?: Error;
}

const DEFAULT_FALLBACK_TEXT = 'Animation unavailable';
const DEFAULT_MAX_RETRIES = 2;
const DEFAULT_RETRY_DELAY = 1000;

const isValidRiveUrl = (url: string): boolean => {
  if (!url || typeof url !== 'string') return false;

  try {
    const urlObj = new URL(url);
    return (
      (urlObj.protocol === 'https:' || urlObj.protocol === 'http:') &&
      url.toLowerCase().includes('.riv')
    );
  } catch {
    return false;
  }
};

const isNetworkError = (error: any): boolean => {
  const errorMessage = error?.message?.toLowerCase() || '';
  return (
    errorMessage.includes('network') ||
    errorMessage.includes('timeout') ||
    errorMessage.includes('404') ||
    errorMessage.includes('fetch') ||
    errorMessage.includes('connection')
  );
};

const isFileCorruptionError = (error: any): boolean => {
  const errorMessage = error?.message?.toLowerCase() || '';
  return (
    errorMessage.includes('corrupt') ||
    errorMessage.includes('invalid') ||
    errorMessage.includes('malformed') ||
    errorMessage.includes('parse')
  );
};

const RiveComponent: React.FC<RiveComponentProps> = (props) => {
  const {
    url,
    resourceName,
    artboardName,
    stateMachineName,
    style,
    autoPlay = true,
    loop = true,
    onLoopEnd,
    fallbackText = DEFAULT_FALLBACK_TEXT,
    maxRetries = DEFAULT_MAX_RETRIES,
    retryDelay = DEFAULT_RETRY_DELAY,
    enableCrashPrevention = true,
  } = props;

  const [state, setState] = useState<RiveState>({
    hasError: false,
    isLoading: true,
    retryCount: 0,
    errorType: 'unknown',
  });

  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const appStateRef = useRef(AppState.currentState);
  const isComponentMountedRef = useRef(true);

  const memoizedStyle = React.useMemo(() => style, [style]);

  useEffect(() => {
    setState({
      hasError: false,
      isLoading: true,
      retryCount: 0,
      errorType: 'unknown',
    });
  }, [url]);

  useEffect(() => {
    const handleAppStateChange = (nextAppState: string) => {
      if (
        appStateRef.current.match(/inactive|background/) &&
        nextAppState === 'active'
      ) {
        if (state.hasError && state.errorType === 'runtime') {
          setState((prev) => ({
            ...prev,
            hasError: false,
            isLoading: true,
            retryCount: 0,
          }));
        }
      }
      appStateRef.current = nextAppState;
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );
    return () => subscription?.remove();
  }, [state.hasError, state.errorType]);

  useEffect(
    () => () => {
      isComponentMountedRef.current = false;
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    },
    [],
  );

  const retryLoad = useCallback(() => {
    if (!isComponentMountedRef.current) return;

    if (state.retryCount < maxRetries) {
      setState((prev) => ({
        ...prev,
        hasError: false,
        isLoading: true,
        retryCount: prev.retryCount + 1,
      }));

      Analytics.track(ANALYTICS_EVENTS.RIVE_ANIMATION_RETRY, {
        url,
        retryCount: state.retryCount + 1,
        errorType: state.errorType,
        maxRetries,
      });
    }
  }, [state.retryCount, state.errorType, maxRetries, url]);

  const handleError = useCallback(
    (error: any) => {
      if (!isComponentMountedRef.current) return;

      let errorType: RiveState['errorType'] = 'unknown';

      if (isNetworkError(error)) {
        errorType = 'network';
      } else if (isFileCorruptionError(error)) {
        errorType = 'file';
      } else {
        errorType = 'runtime';
      }

      setState((prev) => ({
        ...prev,
        hasError: true,
        isLoading: false,
        errorType,
        lastError: error,
      }));

      Analytics.track(ANALYTICS_EVENTS.RIVE_ANIMATION_ERROR, {
        url,
        errorType,
        errorMessage: error?.message || 'Unknown error',
        retryCount: state.retryCount,
        artboardName,
        stateMachineName,
      });

      if (errorType === 'network' && state.retryCount < maxRetries) {
        retryTimeoutRef.current = setTimeout(
          () => {
            retryLoad();
          },
          retryDelay * (state.retryCount + 1),
        );
      }
    },
    [
      state.retryCount,
      maxRetries,
      retryDelay,
      url,
      artboardName,
      stateMachineName,
      retryLoad,
    ],
  );

  const handleLoad = useCallback(() => {
    if (!isComponentMountedRef.current) return;

    setState((prev) => ({
      ...prev,
      hasError: false,
      isLoading: false,
    }));

    Analytics.track(ANALYTICS_EVENTS.RIVE_ANIMATION_LOADED, {
      url,
      retryCount: state.retryCount,
      artboardName,
      stateMachineName,
    });
  }, [url, state.retryCount, artboardName, stateMachineName]);

  const handleLoopEnd = useCallback(() => {
    try {
      onLoopEnd?.();
    } catch (error) {
      Analytics.track(ANALYTICS_EVENTS.RIVE_ANIMATION_CALLBACK_ERROR, {
        url,
        errorMessage: error?.message || 'Loop end callback error',
      });
    }
  }, [onLoopEnd, url]);

  if (!isValidRiveUrl(url)) {
    return (
      <View style={memoizedStyle}>
        <Text style={{ color: dark.colors.textDark, textAlign: 'center' }}>
          {fallbackText}
        </Text>
      </View>
    );
  }

  if (state.hasError) {
    return (
      <View style={memoizedStyle}>
        <Text style={{ color: dark.colors.textDark, textAlign: 'center' }}>
          {fallbackText}
        </Text>
      </View>
    );
  }

  if (enableCrashPrevention) {
    try {
      return (
        <Rive
          url={url}
          resourceName={resourceName}
          stateMachineName={stateMachineName}
          artboardName={artboardName}
          style={memoizedStyle}
          autoplay={autoPlay}
          loop={loop}
          onLoopEnd={handleLoopEnd}
          onError={handleError}
          onLoad={handleLoad}
        />
      );
    } catch (error) {
      handleError(error);
      return (
        <View style={memoizedStyle}>
          <Text style={{ color: dark.colors.textDark, textAlign: 'center' }}>
            {fallbackText}
          </Text>
        </View>
      );
    }
  }

  return (
    <Rive
      url={url}
      resourceName={resourceName}
      stateMachineName={stateMachineName}
      artboardName={artboardName}
      style={memoizedStyle}
      autoplay={autoPlay}
      loop={loop}
      onLoopEnd={handleLoopEnd}
      onError={handleError}
      onLoad={handleLoad}
    />
  );
};

export default React.memo(RiveComponent);
