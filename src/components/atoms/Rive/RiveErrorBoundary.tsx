import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Text, View, ViewStyle } from 'react-native';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import dark from 'core/constants/themes/dark';

interface Props {
  children: ReactNode;
  fallbackText?: string;
  style?: ViewStyle;
  url?: string;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

class RiveErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    this.setState({
      errorInfo,
    });

    Analytics.track(ANALYTICS_EVENTS.RIVE_ANIMATION_ERROR, {
      url: this?.props?.url || 'unknown',
      errorMessage: error?.message,
      errorStack: error?.stack,
      componentStack: errorInfo?.componentStack,
      errorBoundary: 'RiveErrorBoundary',
      timestamp: new Date().toISOString(),
    });

    // Call the onError callback if provided
    if (this?.props?.onError) {
      this?.props?.onError?.(error, errorInfo);
    }

    if (__DEV__) {
      console.error('Rive Error Boundary caught an error:', error);
      console.error('Error Info:', errorInfo);
    }
  }

  resetError = (): void => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  render(): ReactNode {
    const { hasError } = this.state;
    const {
      children,
      fallbackText = 'Animation unavailable',
      style,
    } = this.props;

    if (hasError) {
      return (
        <View style={style}>
          <Text
            style={{
              color: dark.colors.textDark,
              textAlign: 'center',
              fontSize: 12,
              opacity: 0.7,
            }}
          >
            {fallbackText}
          </Text>
        </View>
      );
    }

    return children;
  }
}

export default RiveErrorBoundary;
