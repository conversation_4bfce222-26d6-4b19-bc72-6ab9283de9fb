import { StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  button: {
    borderRadius: 10,
    overflow: 'hidden',
    width: '100%',
    height: '100%',
    backgroundColor: dark.colors.primary,
  },
  buttonSecondary: {
    backgroundColor: dark.colors.cardBackground,
  },
  buttonContent: {
    paddingVertical: 12,
    width: '100%',
    height: '100%',
    elevation: 5, // For Android 3D effect
    shadowColor: '#000', // iOS shadow
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    transform: [{ translateY: 0 }], // Default position
    // transition: 'transform 0.1s', // Removed for React Native compatibility
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  buttonBorderBackground: {
    backgroundColor: '#388E3C',
    width: '100%',
    height: 20,
    borderRadius: 10,
    position: 'absolute',
    bottom: -5,
  },
  buttonBorderBackgroundSecondary: {
    backgroundColor: 'white',
    opacity: 0.2,
  },
  pressedButton: {
    transform: [{ translateY: 4 }],
    borderBottomWidth: 0,
  },
  text: {
    color: dark.colors.primary,
    fontSize: 11,
    fontWeight: '600',
    textAlign: 'center',
    fontFamily: 'Montserrat-600',
    // numberOfLines: 1, // This prop is applied directly to the Text component
    flexShrink: 1,
  },
  textSecondary: {
    color: 'white',
  },
  hoveredButton: {
    // backgroundColor: '#9ee894',
  },
  hoveredButtonSecondary: {
    backgroundColor: dark.colors.background,
  },
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  trailingIconContainer: {
    marginLeft: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  textWithIcon: {
    marginLeft: 0,
  },
  textWithTrailingIcon: {
    marginRight: 0,
  },
  disabledButton: {
    opacity: 0.5, // Standard way to show disabled state
    // You might also want to change background color or prevent press feedback visually
  },
});

export default styles;
