import React, { useCallback } from 'react';
import { Platform, Text, View } from 'react-native';
import Icon from 'atoms/Icon';
import styles from './InteractivePrimaryButton.style';
import Pressable from '../Pressable';

export const BUTTON_TYPES = {
  PRIMARY: 'PRIMARY',
  SECONDARY: 'SECONDARY',
};

interface InteractivePrimaryButtonProps {
  label?: string;
  onPress: () => void;
  buttonStyle?: any;
  labelStyle?: any;
  iconConfig?: {
    type: string; // Should ideally be IconProps['type'] or ICON_TYPES
    name: string;
    size?: number;
    color?: string;
  };
  trailingIconConfig?: {
    type: string; // Should ideally be IconProps['type'] or ICON_TYPES
    name: string;
    size?: number;
    color?: string;
  };
  type?: string;
  buttonContainerStyle?: any;
  radius?: any;
  isSelected?: boolean;
  disabled?: boolean; // Added disabled prop
  buttonContentStyles?: any;
  buttonBorderBackgroundStyle?: any;
}

const InteractivePrimaryButton = ({
  label,
  onPress,
  buttonStyle,
  labelStyle,
  type = BUTTON_TYPES.PRIMARY,
  iconConfig,
  trailingIconConfig,
  buttonContainerStyle,
  disabled = false, // Added disabled prop
  buttonContentStyles,
  buttonBorderBackgroundStyle,
}: InteractivePrimaryButtonProps) => {
  const handleOnPress = useCallback(() => {
    if (disabled) {
      return;
    }
    onPress?.();
  }, [onPress, disabled]);

  return (
    <View style={buttonContainerStyle}>
      <View
        style={[
          styles.buttonBorderBackground,
          type === BUTTON_TYPES.SECONDARY &&
            styles.buttonBorderBackgroundSecondary,
          buttonBorderBackgroundStyle,
        ]}
      />
      <Pressable
        style={({ pressed }) => [ // Removed 'hovered' from callback params
          styles.button,
          pressed && styles.pressedButton,
          // Platform.OS === 'web' && hovered && styles.hoveredButton, // 'hovered' state needs to be managed differently if required
          buttonStyle,
          type === BUTTON_TYPES.SECONDARY && styles.buttonSecondary,
          disabled && styles.disabledButton, // Added style for disabled state
        ]}
        onPress={handleOnPress}
      >
        <View style={[styles.buttonContent, buttonContentStyles]}>
          {iconConfig && (
            <View style={styles.iconContainer}>
              <Icon
                name={iconConfig.name}
                type={iconConfig.type as any} // Cast to any as a temporary fix
                size={iconConfig.size || 18}
                color={iconConfig.color || 'white'}
              />
            </View>
          )}
          {label && (
            <Text
              numberOfLines={1}
              style={[
                styles.text,
                type === BUTTON_TYPES.SECONDARY && styles.textSecondary,
                labelStyle,
                iconConfig && styles.textWithIcon,
                trailingIconConfig && styles.textWithTrailingIcon,
              ]}
            >
              {label}
            </Text>
          )}
          {trailingIconConfig && (
            <View style={styles.trailingIconContainer}>
              <Icon
                name={trailingIconConfig.name}
                type={trailingIconConfig.type as any} // Cast to any as a temporary fix
                size={trailingIconConfig.size || 16}
                color={trailingIconConfig.color || 'white'}
              />
            </View>
          )}
        </View>
      </Pressable>
    </View>
  );
};

export default React.memo(InteractivePrimaryButton);
