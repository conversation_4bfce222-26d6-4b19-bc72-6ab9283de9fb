import { Text } from 'react-native';
import React from 'react';
import useCountDownTimer from 'core/hooks/useCountDownTimerV2';

const TimerText = ({ timer, shouldShowIfFinished, style }: { timer: number, shouldShowIfFinished?: boolean, style?: any }) => {
    const { formattedTime, timer: timerLeft } = useCountDownTimer({ targetTimeStamp: timer });
    if (!shouldShowIfFinished && timerLeft < 1000) {
        return null;
    }
  return <Text style={style}>{formattedTime}</Text>;
};

export default React.memo(TimerText);
