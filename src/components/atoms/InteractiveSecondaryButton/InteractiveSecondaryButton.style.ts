import { Platform, StyleSheet } from 'react-native';
import dark from 'core/constants/themes/dark';

const styles = StyleSheet.create({
  button: {
    borderRadius: 10,
    overflow: 'hidden',
    width: '100%',
    height: '100%',
    borderColor: dark.colors.tertiary,
    borderWidth: 1,
    backgroundColor: dark.colors.background,
  },
  buttonContent: {
    width: '100%',
    height: '100%',
    elevation: 5, // For Android 3D effect
    shadowColor: '#000', // iOS shadow
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    transform: [{ translateY: 0 }], // Default position
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    ...Platform.select({
      web: {
        transition: 'transform 0.1s',
      },
    }),
  },
  buttonBorderBackground: {
    backgroundColor: dark.colors.tertiary,
    width: '100%',
    height: 20,
    borderRadius: 10,
    position: 'absolute',
    bottom: -5,
  },
  pressedButton: {
    transform: [{ translateY: 4 }],
    borderBottomWidth: 0,
  },
  text: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Montserrat-600',
    textAlign: 'center',
  },
  hoveredButton: {
    backgroundColor: 'black',
  },
  hoveredButtonSecondary: {
    backgroundColor: dark.colors.background,
  },
  disabledStyle: {
    opacity: 0.5,
  },
});

export default styles;
