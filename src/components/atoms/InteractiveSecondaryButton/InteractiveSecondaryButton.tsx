import React, { forwardRef, useCallback } from 'react';
import { Text, View } from 'react-native';
import Icon from 'atoms/Icon';
import _size from 'lodash/size';
import Haptics from 'core/container/Haptics';
import styles from './InteractiveSecondaryButton.style';
import Pressable from '../Pressable';

interface InteractiveSecondaryButtonProps {
  testID?: string;
  label?: string;
  onPress: () => void;
  buttonStyle?: any;
  iconConfig?: any;
  labelStyle?: any;
  buttonContentStyle?: any;
  type?: string;
  borderColor?: string;
  borderComponentStyle?: any;
  buttonContainerStyle?: any;
  buttonBackgroundStyle?: any;
  disabled?: boolean;
}

const InteractiveSecondaryButton = forwardRef<
  View,
  InteractiveSecondaryButtonProps
>(
  (
    {
      testID,
      label,
      onPress,
      buttonStyle,
      borderComponentStyle,
      labelStyle,
      iconConfig,
      borderColor,
      buttonContentStyle,
      buttonContainerStyle,
      buttonBackgroundStyle,
      disabled,
    },
    ref,
  ) => {
    const onPressButton = useCallback(() => {
      if (disabled) return;
      onPress?.();
    }, [onPress, disabled]);

    return (
      <View
        testID={testID}
        ref={ref}
        style={[
          { height: 48 },
          buttonContainerStyle,
          disabled && styles.disabledStyle,
        ]}
      >
        <View
          style={[
            styles.buttonBorderBackground,
            borderColor && { backgroundColor: borderColor },
            buttonBackgroundStyle,
            borderComponentStyle && borderComponentStyle,
          ]}
        />
        <Pressable
          style={({ pressed }) => [
            styles.button,
            pressed && !disabled && styles.pressedButton,
            borderColor && { borderColor },
            buttonStyle,
          ]}
          onPress={onPressButton}
          impactFeedbackStyle={Haptics.ImpactFeedbackStyle.Soft}
          disabled={disabled}
        >
          <View style={[styles.buttonContent, buttonContentStyle]}>
            {iconConfig && <Icon size={18} color="white" {...iconConfig} />}
            {_size(label) > 0 && (
              <Text style={[styles.text, labelStyle]}>{label}</Text>
            )}
          </View>
        </Pressable>
      </View>
    );
  },
);

InteractiveSecondaryButton.displayName = 'InteractiveSecondaryButton';

export default React.memo(InteractiveSecondaryButton);
