import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Platform, TextInput } from 'react-native';

const SecureTextInput = ({
  value,
  onChangeText,
  onSecurityViolation,
  securityConfig = {},
  style,
  placeholder,
  editable = true,
  ...otherProps
}) => {
  // Default security configuration
  const defaultConfig = {
    minInputInterval: 30, // Minimum milliseconds between inputs
    maxInputSpeed: 20, // Maximum characters per second
    enableTrustedEventCheck: true, // Check isTrusted on web
    enableTimingAnalysis: true,
    enablePatternDetection: true,
    suspiciousActivityThreshold: 3, // Number of violations before flagging
    resetTimeWindow: 5000, // Reset violations after 5 seconds
    logViolations: true,
    blockSuspiciousInput: true,
  };

  const valueRef = useRef(value);
  const textInputRef = useRef<any>(null);

  useEffect(() => {
    valueRef.current = value;
  }, [value]);

  const config = { ...defaultConfig, ...securityConfig };

  useEffect(() => {
    if (Platform.OS !== 'web') return;
    const handleBlur = () => {
      textInputRef.current?.focus();
    };
    if (textInputRef.current) {
      textInputRef.current?.addEventListener('blur', handleBlur);
    }
    return () => {
      if (textInputRef.current) {
        textInputRef.current?.removeEventListener('blur', handleBlur);
      }
    };
  }, []);

  // Security state
  const securityMetrics = useRef({
    lastInputTime: 0,
    inputCount: 0,
    violations: [],
    typingPattern: [],
    isBlocked: false,
    lastViolationTime: 0,
  });

  const [securityStatus, setSecurityStatus] = useState('normal'); // 'normal', 'warning', 'blocked'

  // Reset violations after time window
  const resetViolationsIfExpired = useCallback(() => {
    const now = Date.now();
    const metrics = securityMetrics.current;

    if (now - metrics.lastViolationTime > config.resetTimeWindow) {
      metrics.violations = [];
      metrics.isBlocked = false;
      setSecurityStatus('normal');
    }
  }, [config.resetTimeWindow]);

  // Log security violation
  const logViolation = useCallback(
    (type, details) => {
      const violation = {
        type,
        timestamp: Date.now(),
        details,
      };

      securityMetrics.current.violations.push(violation);
      securityMetrics.current.lastViolationTime = violation.timestamp;

      if (config.logViolations) {
        console.warn(`Security violation detected: ${type}`, details);
      }

      // Check if threshold exceeded
      const recentViolations = securityMetrics.current.violations.filter(
        (v) => Date.now() - v.timestamp < config.resetTimeWindow,
      );

      if (recentViolations.length >= config.suspiciousActivityThreshold) {
        securityMetrics.current.isBlocked = true;
        setSecurityStatus('blocked');

        if (onSecurityViolation) {
          onSecurityViolation({
            type: 'SUSPICIOUS_ACTIVITY',
            violations: recentViolations,
            isBlocked: true,
          });
        }
      } else {
        setSecurityStatus('warning');

        if (onSecurityViolation) {
          onSecurityViolation({
            type: violation.type,
            violation,
            isBlocked: false,
          });
        }
      }
    },
    [
      config.logViolations,
      config.suspiciousActivityThreshold,
      config.resetTimeWindow,
      onSecurityViolation,
    ],
  );

  // Validate input security
  const validateInputSecurity = useCallback(
    (text, event) => {
      resetViolationsIfExpired();

      const now = Date.now();
      const metrics = securityMetrics.current;

      // If already blocked, reject input
      if (metrics.isBlocked && config.blockSuspiciousInput) {
        return false;
      }

      // Web-specific: Check isTrusted event
      if (
        Platform.OS === 'web' &&
        config.enableTrustedEventCheck &&
        event?.nativeEvent
      ) {
        if (!event.nativeEvent.isTrusted) {
          logViolation('UNTRUSTED_EVENT', {
            eventType: event.nativeEvent.type || 'unknown',
            isTrusted: false,
          });

          if (config.blockSuspiciousInput) {
            return false;
          }
        }
      }

      // Pattern detection
      if (config.enablePatternDetection) {
        const timeDiff = now - metrics.lastInputTime;

        metrics.typingPattern.push({
          timestamp: now,
          interval: timeDiff,
          textLength: text.length,
          charAdded: text.slice(-1),
        });

        // Keep only recent patterns (last 20 inputs)
        if (metrics.typingPattern.length > 20) {
          metrics.typingPattern.shift();
        }

        // Analyze for suspicious patterns
        if (metrics.typingPattern.length >= 5) {
          const recentPatterns = metrics.typingPattern.slice(-5);
          const intervals = recentPatterns
            .map((p) => p.interval)
            .filter((i) => i > 0);

          // Check for too consistent timing (robotic behavior)
          if (intervals.length >= 3) {
            const avgInterval =
              intervals.reduce((a, b) => a + b, 0) / intervals.length;
            const variance =
              intervals.reduce(
                (sum, interval) => sum + (interval - avgInterval) ** 2,
                0,
              ) / intervals.length;
            const standardDeviation = Math.sqrt(variance);
            // Very low variance suggests automated input
            if (standardDeviation < 5 && avgInterval < 100) {
              logViolation('ROBOTIC_PATTERN', {
                averageInterval: avgInterval,
                standardDeviation,
                intervals,
              });
            }
          }
        }
      }

      // Update metrics
      metrics.lastInputTime = now;
      metrics.inputCount++;

      return true;
    },
    [config, logViolation, resetViolationsIfExpired],
  );

  // Handle text change with security validation
  const handleTextChange = useCallback(
    (text: string, event: any) => {
      // Validate security first
      if (!validateInputSecurity(text, event)) {
        return; // Block the input
      }

      // Update internal state
      valueRef.current = text;

      // Call parent handler
      if (onChangeText) {
        onChangeText(text);
      }
    },
    [validateInputSecurity, onChangeText],
  );

  // Handle native events (for web platform)
  const handleNativeEvent = useCallback(
    (event) => {
      const text = event.target?.value || '';
      handleTextChange(text, { nativeEvent: event });
    },
    [handleTextChange],
  );

  // Get security status color
  const getSecurityStatusColor = () => {
    switch (securityStatus) {
      case 'warning':
        return '#FFA500';
      case 'blocked':
        return '#FF0000';
      default:
        return '#00FF00';
    }
  };

  // Public methods for parent components
  const getSecurityMetrics = useCallback(
    () => ({
      ...securityMetrics.current,
      securityStatus,
      isBlocked: securityMetrics.current.isBlocked,
    }),
    [securityStatus],
  );

  const resetSecurity = useCallback(() => {
    securityMetrics.current = {
      lastInputTime: 0,
      inputCount: 0,
      violations: [],
      typingPattern: [],
      isBlocked: false,
      lastViolationTime: 0,
    };
    setSecurityStatus('normal');
  }, []);

  // Expose methods to parent via ref
  React.useImperativeHandle(otherProps.ref, () => ({
    getSecurityMetrics,
    resetSecurity,
    isBlocked: () => securityMetrics.current.isBlocked,
    getViolations: () => securityMetrics.current.violations,
  }));

  // Web-specific event handlers
  const webProps =
    Platform.OS === 'web'
      ? {
          onChange: handleNativeEvent,
          onInput: handleNativeEvent,
        }
      : {};

  return (
    <TextInput
      {...otherProps}
      {...webProps}
      value={value}
      onChangeText={handleTextChange}
      ref={textInputRef}
      style={style}
      placeholder={placeholder}
      editable={editable && !securityMetrics.current.isBlocked}
    />
  );
};

export default SecureTextInput;
