import { StyleSheet } from 'react-native';
import dark from '@/src/core/constants/themes/dark'; // Assuming theme path

export default StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  container: {
    backgroundColor: dark.colors.background,
    borderRadius: 12,
    padding: 24,
    width: '90%',
    maxWidth: 400,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  titleText: {
    fontSize: 15,
    fontWeight: 'Montserrat-500',
    color: dark.colors.textLight,
    marginBottom: 12,
    textAlign: 'center',
  },
  messageText: {
    fontSize: 13,
    color: dark.colors.textDark,
    marginBottom: 24,
    fontFamily: 'Montserrat-500',
    textAlign: 'center',
    lineHeight: 22,
  },
  buttonRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    gap: 30,
    width: '100%',
  },
  cancelButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  cancelButtonText: {
    fontSize: 12,
    color: dark.colors.textDark,
    textAlign: 'center',
    fontFamily: 'Montserrat-600',
  },
  confirmButton: {
    flex: 1,
    marginLeft: 10,
  },
  dangerButton: {
    backgroundColor: dark.colors.error,
  },
  confirmButtonLabel: {
    fontSize: 12,
    fontWeight: 'Montserrat-500',
  },
});
