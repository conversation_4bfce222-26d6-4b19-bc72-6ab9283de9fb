import React, { useCallback, useEffect, useState } from 'react';
import { Overlay } from '@rneui/themed';
import { Pressable, Text, View } from 'react-native';
import Entypo from '@expo/vector-icons/Entypo';
import dark from 'core/constants/themes/dark';
import Rive from 'atoms/Rive';
import RIVE_ANIMATIONS from 'core/constants/riveAnimations';
import { useRouter } from 'expo-router';
import useMediaQuery from 'core/hooks/useMediaQuery';
import Analytics from 'core/analytics';
import { ANALYTICS_EVENTS } from 'core/analytics/const';
import useLocalCache from 'core/hooks/useLocalCache';
import { GAME_TYPES } from 'modules/game/constants/game';
import styles from './WhatsNewOverlay.style';

const WHATS_NEW_KEY = 'whatsnew';
const FEATURE_KEY = 'ability-duels';

const WhatsNewOverlay = () => {
  const [isVisible, setIsVisible] = useState(false);
  const router = useRouter();
  const { getData, setData } = useLocalCache(WHATS_NEW_KEY);

  const { isMobile } = useMediaQuery();

  const closeOverlay = useCallback(() => {
    Analytics.track(ANALYTICS_EVENTS.WHATS_NEW.CLOSED_MODAL, {
      whatsNewKey: FEATURE_KEY,
    });
    setIsVisible(false);
  }, []);

  const onPressSeeMorePuzzle = useCallback(() => {
    setIsVisible(false);
    Analytics.track(ANALYTICS_EVENTS.WHATS_NEW.CLICKED_ON_SEE_MORE, {
      whatsNewKey: FEATURE_KEY,
    });
    router.push(`/search?timeLimit=2&gameType=${GAME_TYPES.DMAS_ABILITY}`);
  }, [router]);

  useEffect(() => {
    getData().then((data) => {
      const hasFeature = data?.[FEATURE_KEY] ?? false;
      if (!hasFeature) {
        Analytics.track(ANALYTICS_EVENTS.WHATS_NEW.VIEW, {
          whatsNewKey: FEATURE_KEY,
        });
        // show whats new modal after 2 seconds
        setTimeout(() => {
          setIsVisible(true);
          setData({
            [FEATURE_KEY]: true,
          });
        }, 1000);
      }
    });
  }, []);

  return (
    <Overlay
      isVisible={isVisible}
      overlayStyle={[
        styles.contentContainer,
        isMobile && styles.compactContentContainer,
      ]}
      animationType="slide"
      onBackdropPress={closeOverlay}
    >
      <View style={styles.headerRow}>
        <View style={{ height: 40, width: 22 }} />
        <Text style={styles.headerText}>What's New?</Text>
        <Entypo
          name="cross"
          size={22}
          color={dark.colors.textDark}
          onPress={closeOverlay}
        />
      </View>
      <View style={styles.qrRow}>
        <Rive
          url={RIVE_ANIMATIONS.ABILITY_DUELS_ANIMATION}
          autoPlay
          style={{ width: 160, height: 160 }}
        />
      </View>
      <View style={styles.footerContent}>
        <Text
          style={{
            color: 'white',
            fontSize: 24,
            fontFamily: 'Montserrat-800',
          }}
        >
          Ability Duels
        </Text>
        <Text style={styles.footerText}>
          You can now play ability questions like LCM, HCF, Sum of Squares,
          Prime Factorization, and Modes related questions with people online.
        </Text>
        <Pressable onPress={onPressSeeMorePuzzle} style={styles.SeeMoreButton}>
          <View>
            <Text style={styles.seeMoreText}>Try Now</Text>
          </View>
        </Pressable>
      </View>
    </Overlay>
  );
};

export default React.memo(WhatsNewOverlay);
