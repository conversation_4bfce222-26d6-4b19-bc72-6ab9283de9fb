import React from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import InteractivePrimaryButton from 'atoms/InteractivePrimaryButton';
import dark from 'core/constants/themes/dark';
import DefaultCollegeIcon from 'atoms/DefaultCollegeIcon';
import _isNil from 'lodash/isNil';
import _isFunction from 'lodash/isFunction';
import styles from './ActionPopoverContent.style';

export interface ActionPopoverContentProps {
  iconPlaceholder?: boolean;
  title: string;
  titleColor?: string;
  message: string;
  primaryActionText: string;
  onPrimaryAction: () => void;
  secondaryActionText: string;
  onSecondaryAction: () => void;
  primaryActionLoading?: boolean;
  renderIconPlaceholder?: () => React.ReactNode;
}

const ActionPopoverContent: React.FC<ActionPopoverContentProps> = ({
  iconPlaceholder = true,
  title,
  titleColor,
  message,
  primaryActionText,
  renderIconPlaceholder,
  onPrimaryAction,
  secondaryActionText,
  onSecondaryAction,
  primaryActionLoading = false,
}) => {
  const renderIconPlaceholderComponent = () => {
    if (!iconPlaceholder) {
      return null;
    }
    if (!_isNil(renderIconPlaceholder) && _isFunction(renderIconPlaceholder)) {
      return renderIconPlaceholder?.();
    }
    return (
      <View style={styles.popoverImagePlaceholder}>
        <DefaultCollegeIcon size={40} />
      </View>
    );
  };

  return (
    <View>
      <View style={styles.popoverCard}>
        {renderIconPlaceholderComponent?.()}
        <Text
          style={[styles.popoverTitle, titleColor ? { color: titleColor } : {}]}
        >
          {title}
        </Text>
        <Text style={styles.popoverMessage}>{message}</Text>

        <InteractivePrimaryButton
          label={primaryActionLoading ? 'PROCESSING...' : primaryActionText}
          onPress={onPrimaryAction}
          buttonStyle={styles.popoverPrimaryButton}
          labelStyle={styles.popoverPrimaryButtonText}
          buttonContainerStyle={{
            height: 42,
            width: '100%',
            borderRadius: 8,
          }}
          buttonBorderBackgroundStyle={{ backgroundColor: dark.colors.streak }}
          disabled={primaryActionLoading}
        />
      </View>
      <TouchableOpacity
        onPress={onSecondaryAction}
        disabled={primaryActionLoading}
      >
        <Text style={styles.popoverSecondaryActionText}>
          {secondaryActionText}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default ActionPopoverContent;
