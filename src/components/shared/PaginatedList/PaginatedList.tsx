import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  FlatList,
  RefreshControl,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import _isFunction from 'lodash/isFunction';
import _isNil from 'lodash/isNil';

import Loading from 'atoms/Loading';
import styles from './PaginatedList.style';
import dark from '../../../core/constants/themes/dark';
import { PagesComponentProps, PaginatedListProps } from './types';

const DEFAULT_PAGE_SIZE = 20;

export const PagesComponent: React.FC<PagesComponentProps> = React.memo(
  ({ setPage, totalPages, page }) => {
    const maxPageNumbersToShow = 5;

    const { startPage, endPage, displayedPages } = useMemo(() => {
      let start = Math.max(1, page - Math.floor(maxPageNumbersToShow / 2));
      let end = start + maxPageNumbersToShow - 1;

      if (end > totalPages) {
        end = totalPages;
        start = Math.max(1, end - maxPageNumbersToShow + 1);
      }

      const pages = [...Array(end - start + 1).keys()].map((i) => start + i);

      return { startPage: start, endPage: end, displayedPages: pages };
    }, [page, totalPages, maxPageNumbersToShow]);

    const handlePrevPage = useCallback(() => {
      setPage((prevPage) => Math.max(1, prevPage - 1));
    }, [setPage]);

    const handleNextPage = useCallback(() => {
      setPage((prevPage) => Math.min(totalPages, prevPage + 1));
    }, [setPage, totalPages]);

    const prevButtonColor = useMemo(
      () => (page === 1 ? dark.colors.textDark : '#fff'),
      [page],
    );

    const nextButtonColor = useMemo(
      () => (page === totalPages ? dark.colors.textDark : '#fff'),
      [page, totalPages],
    );

    return (
      <View style={styles.paginationContainer}>
        <TouchableOpacity disabled={page === 1} onPress={handlePrevPage}>
          <Icon name="chevron-left" size={24} color={prevButtonColor} />
        </TouchableOpacity>

        <View style={styles.pageNumbersContainer}>
          {startPage > 1 && <Text style={styles.pageText}>...</Text>}
          {displayedPages.map((pageNumber) => (
            <TouchableOpacity
              key={pageNumber}
              style={[
                styles.pageButton,
                page === pageNumber && styles.selectedPage,
              ]}
              onPress={() => setPage(pageNumber)}
            >
              <Text
                style={[
                  styles.pageTextStyle,
                  page === pageNumber
                    ? styles.selectedPageText
                    : styles.pageText,
                ]}
              >
                {pageNumber}
              </Text>
            </TouchableOpacity>
          ))}
          {endPage < totalPages && <Text style={styles.pageText}>...</Text>}
        </View>
        <TouchableOpacity
          disabled={page === totalPages}
          onPress={handleNextPage}
        >
          <Icon name="chevron-right" size={24} color={nextButtonColor} />
        </TouchableOpacity>
      </View>
    );
  },
);

export interface PaginatedListRef {
  loadData: (isRefreshing?: boolean) => Promise<void>;
  setPage: (page: number) => void;
  onRefresh: () => void;
}

const PaginatedList = forwardRef(
  <T extends { _id?: string }>(
    {
      fetchData,
      renderItem,
      hidePaginaton = false,
      renderHeader,
      pageSize = DEFAULT_PAGE_SIZE,
      keyExtractor,
      contentContainerStyle,
      placeholderComponent,
      listFooterComponent,
      makeHeaderScrollable = false,
      emptyListComponent,
      updateCacheFunction,
      dataKey,
      pullToRefreshEnabled = false,
      showNoDataStateForAllPages = false,
    }: PaginatedListProps<T>,
    ref: React.Ref<PaginatedListRef>,
  ) => {
    const [page, setPage] = useState<number>(1);
    const [data, setData] = useState<T[]>([]);
    const [totalItems, setTotalItems] = useState<number>(0);
    const [dataList, setDataList] = useState<any[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [refreshing, setRefreshing] = useState<boolean>(false);
    const totalPages = Math.ceil(totalItems / pageSize);

    const loadData = useCallback(
      async (isRefreshing = false) => {
        if (isRefreshing) {
          setRefreshing(true);
        } else {
          setLoading(true);
        }

        try {
          const response = await fetchData({ pageNumber: page });
          setData(response.data);
          setTotalItems(response.totalItems);
          if (response.itemsList) {
            setDataList(response.itemsList);
          }
        } catch (error) {
          //
        } finally {
          setLoading(false);
          setRefreshing(false);
        }
      },
      [fetchData, page],
    );

    const onRefresh = useCallback(() => {
      loadData?.(true);
    }, [loadData]);

    const loadDataRef = useRef(loadData);
    loadDataRef.current = loadData;

    useEffect(() => {
      loadDataRef.current?.();
    }, [page]);

    useImperativeHandle(
      ref,
      () => ({
        loadData,
        setPage,
        onRefresh,
      }),
      [loadData, onRefresh],
    );

    const handleItemRemove = useCallback(
      async (itemToRemove: T) => {
        const removedItemId = itemToRemove?._id;
        if (
          !_isNil(updateCacheFunction) &&
          _isFunction(updateCacheFunction) &&
          !_isNil(removedItemId)
        ) {
          await updateCacheFunction({
            removedItemIds: [removedItemId],
            pageNumber: page,
          });
        }
      },
      [page, updateCacheFunction],
    );

    const renderListItem = useCallback(
      ({ item, index }: { item: any; index: number }) =>
        renderItem({
          item,
          index,
          onRemove: (itemAgrs) => {
            console.info('RITESH: itemAgrs', itemAgrs);
            const itemToBePassed = _isNil(itemAgrs) ? item : itemAgrs;
            handleItemRemove(itemToBePassed);
          },
        }),
      [renderItem, handleItemRemove],
    );

    const renderContent = () => {
      if (loading && !refreshing) {
        return placeholderComponent ? (
          placeholderComponent()
        ) : (
          <Loading label="Loading..." />
        );
      }

      if (pullToRefreshEnabled && refreshing) {
        return <Loading label="Refreshing..." />;
      }

      return (
        <FlatList
          data={data}
          renderItem={renderListItem}
          keyExtractor={keyExtractor}
          ListHeaderComponent={
            makeHeaderScrollable && renderHeader
              ? () => renderHeader({ page, data, itemsList: dataList })
              : null
          }
          contentContainerStyle={contentContainerStyle}
          ListFooterComponent={listFooterComponent}
          showsVerticalScrollIndicator={false}
          refreshing={pullToRefreshEnabled ? refreshing : false}
          onRefresh={pullToRefreshEnabled ? onRefresh : undefined}
          refreshControl={
            pullToRefreshEnabled ? (
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                tintColor={dark.colors.secondary}
                colors={[dark.colors.secondary]}
                progressBackgroundColor={dark.colors.primary}
                progressViewOffset={10}
                title="Refreshing..."
                titleColor={dark.colors.secondary}
              />
            ) : undefined
          }
          ListEmptyComponent={
            emptyListComponent || (
              <Text style={styles.emptyStateText}>
                {page > 1 ? `NO DATA ON PAGE ${page}` : 'NO DATA'}
              </Text>
            )
          }
        />
      );
    };

    return (
      <View style={styles.container}>
        {renderHeader && !makeHeaderScrollable
          ? renderHeader({ page, data, itemsList: dataList })
          : null}
        {renderContent()}
        {!hidePaginaton && !loading && totalPages > 1 ? (
          <PagesComponent
            setPage={setPage}
            totalPages={totalPages}
            page={page}
          />
        ) : null}
      </View>
    );
  },
);

export default React.memo(PaginatedList);
