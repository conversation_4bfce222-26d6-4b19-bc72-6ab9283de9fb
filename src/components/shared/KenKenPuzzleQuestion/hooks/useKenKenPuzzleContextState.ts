import { useEffect, useMemo, useRef } from 'react';
import getCurrentTimeWithOffset from 'core/utils/getCurrentTimeWithOffset';
import _isNil from 'lodash/isNil';
import { parseKenKenString } from 'modules/puzzles/utils/kenkenPuzzleGenerator';
import { PuzzleType } from 'modules/puzzles/types/puzzleType';
import puzzleReader from 'modules/puzzles/readers/puzzleReader';
import { KenKenPuzzleQuestionContextValue } from '../context/context';
import useTimeSpentInKenKenPuzzle from './useTimeSpentInKenKenPuzzle';
import useGridStateInKenKenPuzzle from './useGridStateInKenKenPuzzle';
import { useKenKenPuzzleReducer } from '../reducers/kenkenPuzzleReducer';

export const KENKEN_PUZZLE_ACTION_TYPES = {
  SELECT_CELL: 'SELECT_CELL',
  INPUT_VALUE: 'INPUT_VALUE',
  TOGGLE_PENCIL_MODE: 'TOGGLE_PENCIL_MODE',
  TOGGLE_PENCIL_MARK: 'TOGGLE_PENCIL_MARK',
  UNDO: 'UNDO',
  REDO: 'REDO',
  CHECK_SOLUTION: 'CHECK_SOLUTION',
  RESET_STATE: 'RESET_STATE',
  CLEAR_ALL: 'CLEAR_ALL',
};

interface UseKenKenPuzzleContextStateProps {
  puzzle: PuzzleType;
  onSubmitPuzzle: ({ timeSpent }: { timeSpent: number }) => void;
  shouldCacheTime?: boolean;
  shouldCacheGrid?: boolean;
  onWrongCombination?: () => void;
  onWrongBoxFill?: () => void;
  cachedUserInputs?: (number | null)[] | null;
  cachedPencilMarks?: number[][] | null;
}

const useKenKenPuzzleContextState = ({
  puzzle: puzzleObject,
  onSubmitPuzzle,
  onWrongCombination,
  shouldCacheTime = false,
  shouldCacheGrid = false,
  cachedUserInputs = null,
  cachedPencilMarks = null,
}: UseKenKenPuzzleContextStateProps): KenKenPuzzleQuestionContextValue => {
  const puzzle = useMemo(
    () => parseKenKenString(puzzleReader.kenKenPuzzleString(puzzleObject)),
    [puzzleObject],
  );

  const [state, dispatch] = useKenKenPuzzleReducer({
    cachedUserInputs,
    puzzle,
    cachedPencilMarks,
  });

  const puzzleId = useMemo(() => puzzleReader.id(puzzleObject), [puzzleObject]);

  const {
    initializeGridStateCache,
    clearGridStateCache,
    updateGridStateCache,
  } = useGridStateInKenKenPuzzle({
    puzzleID: puzzleId,
    hasSolved: false,
    shouldCacheGrid,
  });

  const initGridCacheRef = useRef(initializeGridStateCache);
  initGridCacheRef.current = initializeGridStateCache;

  const updateGridCacheRef = useRef(updateGridStateCache);
  updateGridCacheRef.current = updateGridStateCache;

  const { prevTimeSpent, initializeTimeSpentCache, clearTimeSpentCache } =
    useTimeSpentInKenKenPuzzle({
      startTime: state.startTime,
      puzzleID: puzzleId,
      hasSolved: state.isPuzzleSolved,
      shouldCacheTime,
    });

  const initTimeCacheRef = useRef(initializeTimeSpentCache);
  initTimeCacheRef.current = initializeTimeSpentCache;

  useEffect(() => {
    if (shouldCacheGrid && !state.isPuzzleSolved) {
      const timeoutId = setTimeout(() => {
        updateGridCacheRef.current(state.gridCells, state.isPuzzleSolved);
      }, 300);

      return () => clearTimeout(timeoutId);
    }
    return undefined;
  }, [state.gridCells, shouldCacheGrid, state.isPuzzleSolved]);

  useEffect(() => {
    if (!_isNil(puzzleId)) {
      if (!cachedUserInputs) {
        dispatch({ type: KENKEN_PUZZLE_ACTION_TYPES.RESET_STATE });
      }

      if (shouldCacheTime) {
        initTimeCacheRef.current();
      }

      if (shouldCacheGrid && !cachedUserInputs) {
        initGridCacheRef.current();
      }
    }
  }, [puzzleId, shouldCacheTime, shouldCacheGrid, cachedUserInputs]);

  useEffect(() => {
    if (state.isPuzzleFilled) {
      if (state.isPuzzleSolved && !_isNil(state.startTime)) {
        const timeTaken =
          prevTimeSpent + (getCurrentTimeWithOffset() - state.startTime);
        onSubmitPuzzle({ timeSpent: timeTaken });

        if (shouldCacheTime) {
          clearTimeSpentCache();
        }

        if (shouldCacheGrid) {
          clearGridStateCache();
        }
      } else if (!state.isPuzzleSolved) {
        onWrongCombination?.();
      }
    }
  }, [
    state.isPuzzleFilled,
    state.isPuzzleSolved,
    state.startTime,
    onSubmitPuzzle,
    onWrongCombination,
    prevTimeSpent,
    shouldCacheTime,
    shouldCacheGrid,
    clearTimeSpentCache,
    clearGridStateCache,
  ]);

  const contextState = useMemo(
    () => ({
      ...state,
      prevTimeSpent,
    }),
    [state, prevTimeSpent],
  );

  return {
    state: contextState,
    dispatch,
  };
};

export default useKenKenPuzzleContextState;
