import React, { ReactNode } from 'react';
import { View } from 'react-native';
import {
  Adapt,
  Button,
  Dialog,
  Sheet,
  SnapPointsMode,
  Unspaced,
  YStack,
} from 'tamagui';
import dark from '@/src/core/constants/themes/dark';
import _isNil from 'lodash/isNil';
import _isFunction from 'lodash/isFunction';
import AntDesign from '@expo/vector-icons/AntDesign';
import defaultStyles from './BottomSheet.style';
import {
  BottomSheetProps,
  BottomSheetState,
  OpenBottomSheetProps,
} from './types';

let instance: BottomSheet | null = null;

class BottomSheet extends React.PureComponent<
  BottomSheetProps,
  BottomSheetState
> {
  constructor(props: BottomSheetProps) {
    super(props);
    this.state = {
      isOpen: false,
      content: null,
      renderFrameComponent: null,
      renderCloseButton: null,
      styles: {},
      dismissOnOverlayPress: true,
      animation: '200ms',
      snapPoints: null,
    };
    instance = this;
  }

  static getInstance(): BottomSheet | null {
    return instance;
  }

  componentWillUnmount() {
    instance = null;
  }

  render() {
    const {
      isOpen,
      content,
      animation = '200ms',
      renderFrameComponent,
      renderCloseButton,
      snapPoints,
      styles = {},
      dismissOnOverlayPress,
    } = this.state;

    if (!isOpen || !content) {
      return null;
    }

    const renderCloseButtonComponent = (): ReactNode => {
      if (!_isNil(renderCloseButton) && _isFunction(renderCloseButton)) {
        return renderCloseButton();
      }
      return (
        <View
          style={{
            top: -45,
            width: '100%',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <View style={{ ...defaultStyles.closeButton, ...styles.closeButton }}>
            <AntDesign
              name="close"
              color={dark.colors.textDark}
              size={22}
              onPress={this.closeBottomSheet}
            />
          </View>
        </View>
      );
    };

    const DefaultFrameComponent = ({
      children,
    }: {
      children: React.ReactNode;
    }) => (
      <Sheet.Frame
        backgroundColor={dark.colors.background}
        borderTopColor={dark.colors.secondary}
        borderTopWidth={4}
        {...styles.frame}
      >
        <Sheet.ScrollView showsVerticalScrollIndicator={false}>
          <YStack space="$4">
            <YStack {...styles.contentContainer}>{children}</YStack>
          </YStack>
        </Sheet.ScrollView>
      </Sheet.Frame>
    );

    const FrameComponent = renderFrameComponent || DefaultFrameComponent;

    const extraProps = _isNil(snapPoints)
      ? {
          snapPointsMode: 'fit' as SnapPointsMode,
        }
      : {
          snapPoints,
          snapPointsMode: 'percent' as SnapPointsMode,
        };

    return (
      <Dialog
        modal
        // zIndex={200000}
        open={isOpen}
        onOpenChange={(open) => {
          if (!open && dismissOnOverlayPress) {
            this.closeBottomSheet();
          }
        }}
      >
        <Adapt when="sm" platform="touch">
          <Sheet
            disableDrag
            forceRemoveScrollEnabled={isOpen}
            modal
            zIndex={200000}
            open={isOpen}
            dismissOnSnapToBottom
            dismissOnOverlayPress={dismissOnOverlayPress}
            animation={animation}
            {...extraProps}
          >
            <Sheet.Overlay
              animation={animation}
              enterStyle={{ opacity: 0 }}
              exitStyle={{ opacity: 0 }}
              onPress={(e) => {
                if (!dismissOnOverlayPress) {
                  e.preventDefault();
                }
              }}
            />
            <Sheet.Handle />
            {dismissOnOverlayPress && renderCloseButtonComponent()}
            <FrameComponent>
              {_isFunction(content)
                ? content({ closeBottomSheet: this.closeBottomSheet })
                : content}
            </FrameComponent>
          </Sheet>
        </Adapt>
        <Dialog.Portal>
          <Dialog.Overlay
            key="overlay"
            animation="slow"
            opacity={0.9}
            enterStyle={{ opacity: 0 }}
            exitStyle={{ opacity: 0 }}
            pointerEvents="none"
          />

          <Dialog.Content
            bordered
            elevate
            borderRadius={4}
            maxWidth={500}
            backgroundColor={dark.colors.background}
            key="content"
            animateOnly={['transform', 'opacity']}
            animation={[
              'quicker',
              {
                opacity: {
                  overshootClamping: true,
                },
              },
            ]}
            enterStyle={{ x: 0, y: -20, opacity: 0, scale: 0.9 }}
            exitStyle={{ x: 0, y: 10, opacity: 0, scale: 0.95 }}
            gap="$4"
            pointerEvents="auto"
          >
            {_isFunction(content)
              ? content({ closeBottomSheet: this.closeBottomSheet })
              : content}
            {dismissOnOverlayPress && (
              <Unspaced>
                <Dialog.Close asChild>
                  <Button
                    position="absolute"
                    top="$3"
                    right="$3"
                    size="$2"
                    circular
                    backgroundColor="transparent"
                    icon={<AntDesign name="close" size={18} color="white" />}
                  />
                </Dialog.Close>
              </Unspaced>
            )}
          </Dialog.Content>
        </Dialog.Portal>
      </Dialog>
    );
  }

  openBottomSheet = (props: OpenBottomSheetProps = {}) => {
    this.setState({
      isOpen: true,
      content: props.content || null,
      renderFrameComponent: props.renderFrameComponent || null,
      renderCloseButton: props.renderCloseButton || null,
      snapPoints: props.snapPoints || null,
      styles: props.styles || {},
      animation: props.animation || '200ms',
      dismissOnOverlayPress: props.dismissOnOverlayPress !== false,
      sheetPosition: props.sheetPosition,
      containerPadding: props.containerPadding,
    });
  };

  closeBottomSheet = () => {
    this.setState({
      isOpen: false,
      content: null,
      renderFrameComponent: null,
      sheetPosition: 1,
    });
  };
}

function openBottomSheet(props: OpenBottomSheetProps = {}) {
  const bottomSheetInstance = BottomSheet.getInstance();
  if (bottomSheetInstance) {
    bottomSheetInstance.openBottomSheet(props);
  }
}

function closeBottomSheet() {
  const bottomSheetInstance = BottomSheet.getInstance();
  if (bottomSheetInstance) {
    bottomSheetInstance.closeBottomSheet();
  }
}

const WrappedBottomSheetComponent = React.forwardRef(
  ({ ...restProps }, ref: React.Ref<BottomSheet>) => (
    <BottomSheet {...restProps} ref={ref} />
  ),
);

export default WrappedBottomSheetComponent;

export {
  WrappedBottomSheetComponent as Component,
  openBottomSheet,
  closeBottomSheet,
};
